import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  model,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AbstractControl,
  FormArray,
  FormControl,
  FormGroup,
  FormsModule,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import { UserDataService } from '@shared/data/userdata.service';
import { Observable } from 'rxjs';
import {
  ActiveCurrencyCode,
  activeCurrencyCodes,
  ChargeBearerType1Code,
  ExternalCategoryPurpose1Code,
  ExternalPurpose1Code,
  ExternalServiceLevel1Code,
  Priority2Code,
  priority2Codes,
  SettlementMethod1Code,
  settlementMethod1Codes,
  User,
} from '@shared/types';
import {
  FormStepperComponent,
  FormTextInputComponent,
  FormSelectComponent,
  SelectOption,
  FormRadioButtonGroupComponent,
  FormNumberInputComponent,
  SelectButtonComponent,
} from '@shared/ui';
import {
  ToggleButtonChangeEvent,
  ToggleButtonModule,
} from 'primeng/togglebutton';
import { formSteps, Pacs008FormGroup } from './pacs008-create.types';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RadioButtonGroupComponent } from '@shared/ui/form/form-fields/form-radio-button-group/radio-button-group/radio-button-group.component';
import {
  SelectButtonChangeEvent,
  SelectButtonModule,
} from 'primeng/selectbutton';
import { ButtonModule } from 'primeng/button';
import {
  AutohideDirective,
  FormRulesDirective,
} from '../../../../../libs/iso20022-angular-lib/projects/iso20022-lib/directives';
import { clientRules } from '../../../../../libs/iso20022-angular-lib/projects/iso20022-lib/rules';

@Component({
  selector: 'app-pacs008',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    FormStepperComponent,
    FormTextInputComponent,
    FormSelectComponent,
    FormRadioButtonGroupComponent,
    FormNumberInputComponent,
    RadioButtonModule,
    RadioButtonGroupComponent,
    SelectButtonComponent,
    ButtonModule,
    FormRulesDirective,
    AutohideDirective,
  ],
  templateUrl: './pacs008.component.html',
  styleUrl: './pacs008.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class Pacs008Component implements OnInit {
  private fb = inject(NonNullableFormBuilder);
  private userDataService = inject(UserDataService);
  userData$?: Observable<User>;

  pacs008FormGroup: Pacs008FormGroup = this.fb.group({
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd': this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry':
      this.fb.control(''),
    'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine':
      this.fb.control(''),
    // 'CdtTrfTxInf-PmtId-InstrId': new FormControl('', {
    //   nonNullable: true,
    // }),
    // 'CdtTrfTxInf-PmtId-EndToEndId': new FormControl('', {
    //   nonNullable: true,
    // }),
    // 'CdtTrfTxInf-PmtTpInf-InstrPrty': new FormControl(null),
    // 'CdtTrfTxInf-PmtTpInf-SvcLvl-Cd': new FormControl(null),
    // 'CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd': new FormControl(null),
    // 'CdtTrfTxInf-Purp-Cd': new FormControl(null),
    // 'CdtTrfTxInf-Purp-Prtry': new FormControl('', {
    //   nonNullable: true,
    // }),
    // 'CdtTrfTxInf-InstdAmt': new FormControl(null),
    // 'CdtTrfTxInf-InstdAmt-Ccy': new FormControl(null),
    // 'CdtTrfTxInf-IntrBkSttlmAmt-Ccy': new FormControl(null),
    // 'CdtTrfTxInf-XchgRate': new FormControl(null),
    // 'CdtTrfTxInf-IntrBkSttlmAmt': new FormControl(null),
    // 'CdtTrfTxInf-ChrgBr': new FormControl(null),
    // 'CdtTrfTxInf-ChrgsInf-Amt': new FormControl('', { nonNullable: true }),
    // 'CdtTrfTxInf-ChrgsInf-Amt-Ccy': new FormControl('', { nonNullable: true }),
    // fieldA: new FormControl('', { nonNullable: true }),
    // fieldB: new FormControl('', { nonNullable: true }),
    // fieldC: new FormControl('', { nonNullable: true }),
    // fieldD: new FormControl('', { nonNullable: true }),
  });

  // Expose form steps, validation rules and select options for use in the template
  formSteps = formSteps;
  pacs008ValidationRules = clientRules;
  settlementMethodOptions: SelectOption<SettlementMethod1Code>[] =
    settlementMethod1Codes.map((code) => ({
      key: code,
      label: code,
      value: code,
    }));
  // paymentPriorityOptions: SelectOption<Priority2Code>[] = priority2Codes.map(
  //   (code) => ({
  //     key: code,
  //     label: code,
  //     value: code,
  //   })
  // );
  // serviceLevelOptions: SelectOption<ExternalServiceLevel1Code>[] = [
  //   { key: 'URGP', label: 'Urgent Payment (URGP)', value: 'URGP' },
  //   { key: 'NURG', label: 'Non-Urgent (NURG)', value: 'NURG' },
  //   { key: 'SEPA', label: 'SEPA', value: 'SEPA' },
  // ];
  // categoryPurposeOptions: SelectOption<ExternalCategoryPurpose1Code>[] = [
  //   { key: 'SUPP', label: 'Supplier Payment (SUPP)', value: 'SUPP' },
  //   { key: 'SALA', label: 'Salary (SALA)', value: 'SALA' },
  //   { key: 'PENS', label: 'Pension (PENS)', value: 'PENS' },
  //   { key: 'SECU', label: 'Securities (SECU)', value: 'SECU' },
  //   { key: 'RPRE', label: 'Represented (RPRE)', value: 'RPRE' },
  // ];
  // useProprietaryPurposeOptions: SelectOption<boolean>[] = [
  //   { key: 'false', label: 'Standard', value: false },
  //   { key: 'true', label: 'Proprietary', value: true },
  // ];
  // purposeOptions: SelectOption<ExternalPurpose1Code>[] = [
  //   { key: 'IVPT', label: 'Invoice Payment (IVPT)', value: 'IVPT' },
  //   { key: 'DIVI', label: 'Dividend Payment (DIVI)', value: 'DIVI' },
  //   { key: 'FEES', label: 'Fees (FEES)', value: 'FEES' },
  //   { key: 'LOAR', label: 'Loan Repayment (LOAR)', value: 'LOAR' },
  //   { key: 'GDSV', label: 'Goods and Services (GDSV)', value: 'GDSV' },
  // ];
  // currencyOptions: SelectOption<ActiveCurrencyCode>[] = activeCurrencyCodes.map(
  //   (code) => ({
  //     key: code,
  //     label: code,
  //     value: code,
  //   })
  // );
  // useCurrencyConversionOptions: SelectOption<boolean>[] = [
  //   { key: 'false', label: 'No, same currency throughout', value: false },
  //   { key: 'true', label: 'Yes, currency conversion is needed', value: true },
  // ];
  // conversionMethodOptions: SelectOption<boolean>[] = [
  //   { key: 'true', label: 'I know the exchange rate', value: true },
  //   { key: 'false', label: 'I know the final settlement amount', value: false },
  // ];
  // chargeBearerOptions: SelectOption<ChargeBearerType1Code>[] = [
  //   { key: 'DEBT', label: 'Charges paid by sender (DEBT)', value: 'DEBT' },
  //   { key: 'CRED', label: 'Charges paid by beneficiary (CRED)', value: 'CRED' },
  //   { key: 'SHAR', label: 'Charges shared (SHAR)', value: 'SHAR' },
  // ];

  // State for "either or" choices, e.g. structured or unstructured address
  // useProprietaryPurpose = signal<boolean | null>(null);
  // onPurposeToggle(event: boolean | null) {
  //   if (event === true) {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-Purp-Cd'].setValue(null);
  //   } else {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-Purp-Prtry'].setValue('');
  //   }
  //   this.useProprietaryPurpose.set(event);
  // }
  // useCurrencyConversion = signal<boolean | null>(null);
  // onCurrencyConversionToggle(event: boolean | null) {
  //   this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt-Ccy'].setValue(
  //     null
  //   );
  //   this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt'].setValue(null);
  //   this.pacs008FormGroup.controls['CdtTrfTxInf-XchgRate'].setValue(null);
  //   this.useCurrencyConversion.set(event);
  // }
  // useExchangeRateConversionMethod = signal<boolean | null>(null);
  // onConversionMethodToggle(event: boolean | null) {
  //   if (event === null) {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-XchgRate'].setValue(null);
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt'].setValue(
  //       null
  //     );
  //   } else {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-XchgRate'].setValue(1);
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt'].setValue(
  //       this.pacs008FormGroup.controls['CdtTrfTxInf-InstdAmt'].value
  //     );
  //   }
  //   this.useExchangeRateConversionMethod.set(event);
  // }
  // onExchangeRateChange(newExchangeRate: number | null) {
  //   const instructedAmount =
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-InstdAmt'].value;
  //   if (newExchangeRate !== null && instructedAmount !== null) {
  //     const interbankSettlementAmount = Number(
  //       (instructedAmount * newExchangeRate).toFixed(2)
  //     );
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt'].setValue(
  //       interbankSettlementAmount
  //     );
  //   } else {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-IntrBkSttlmAmt'].setValue(
  //       null
  //     );
  //   }
  // }
  // onInterbankSettlementAmountChange(
  //   newInterbankSettlementAmount: number | null
  // ) {
  //   const instructedAmount =
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-InstdAmt'].value;
  //   if (newInterbankSettlementAmount !== null && instructedAmount !== null) {
  //     const exchangeRate = Number(
  //       (newInterbankSettlementAmount / instructedAmount).toFixed(4)
  //     );
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-XchgRate'].setValue(
  //       exchangeRate
  //     );
  //   } else {
  //     this.pacs008FormGroup.controls['CdtTrfTxInf-XchgRate'].setValue(null);
  //   }
  // }

  addItem(fieldName: string) {
    const field = this.pacs008FormGroup.get(fieldName);
    if (!field || !(field instanceof FormArray)) {
      console.error(`Field ${fieldName} is not an array or does not exist.`);
      return;
    }
    field.push(this.fb.control(''));
  }

  removeItem(fieldName: string, index: number) {
    const field = this.pacs008FormGroup.get(fieldName);
    if (!field || !(field instanceof FormArray)) {
      console.error(`Field ${fieldName} is not an array or does not exist.`);
      return;
    }
    field.removeAt(index);
  }

  currentStepIndex = signal(0);
  currentControls = computed<AbstractControl[]>(() => {
    const controlNames = this.formSteps[this.currentStepIndex()].controlNames;
    return controlNames.reduce<AbstractControl[]>((acc, name) => {
      const control = this.pacs008FormGroup.get(name);
      if (control) {
        acc.push(control);
      }
      return acc;
    }, []);
  });

  ngOnInit(): void {
    this.userData$ = this.userDataService.getUserData('');
  }

  onStepChange(newStepIndex: number): void {
    // Before changing the step, check if the current step is valid, if the user tries to go to the next step
    if (
      newStepIndex > this.currentStepIndex() &&
      this.isStepInvalid(this.currentStepIndex())
    ) {
      this.markCurrentControlsAsTouched();
      return;
    }

    this.currentStepIndex.set(newStepIndex);
  }

  onSubmit(): void {
    if (this.pacs008FormGroup.valid) {
      console.log('Form submitted:', this.pacs008FormGroup.value);
    } else {
      console.log('Form is invalid. Please check the fields.');
    }
  }

  private isStepInvalid(stepIndex: number): boolean {
    if (stepIndex < 0 || stepIndex >= this.formSteps.length) {
      return true;
    }

    for (const control of this.currentControls()) {
      if (control && control.invalid) {
        return true;
      }
    }

    return false;
  }

  private markCurrentControlsAsTouched(): void {
    for (const control of this.currentControls()) {
      if (control) {
        control.markAsTouched();
      }
    }
    this.pacs008FormGroup.updateValueAndValidity();
  }
}
