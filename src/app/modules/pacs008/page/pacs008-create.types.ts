import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { FormStep } from '@shared/ui';
import {
  ActiveCurrencyCode,
  ChargeBearerType1Code,
  ExternalCategoryPurpose1Code,
  ExternalPurpose1Code,
  ExternalServiceLevel1Code,
  Priority2Code,
} from '@shared/types';

export type Pacs008FormGroup = FormGroup<{
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry': FormControl<string>;
  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine': FormControl<string>;
  // 'CdtTrfTxInf-PmtId-InstrId': FormControl<string>;
  // 'CdtTrfTxInf-PmtId-EndToEndId': FormControl<string>;
  // 'CdtTrfTxInf-PmtTpInf-InstrPrty': FormControl<Priority2Code | null>;
  // 'CdtTrfTxInf-PmtTpInf-SvcLvl-Cd': FormControl<ExternalServiceLevel1Code | null>;
  // 'CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd': FormControl<ExternalCategoryPurpose1Code | null>;
  // 'CdtTrfTxInf-Purp-Cd': FormControl<ExternalPurpose1Code | null>;
  // 'CdtTrfTxInf-Purp-Prtry': FormControl<string>;
  // 'CdtTrfTxInf-InstdAmt': FormControl<null>;
  // 'CdtTrfTxInf-InstdAmt-Ccy': FormControl<ActiveCurrencyCode | null>;
  // 'CdtTrfTxInf-IntrBkSttlmAmt-Ccy': FormControl<ActiveCurrencyCode | null>;
  // 'CdtTrfTxInf-XchgRate': FormControl<number | null>;
  // 'CdtTrfTxInf-IntrBkSttlmAmt': FormControl<number | null>;
  // 'CdtTrfTxInf-ChrgBr': FormControl<ChargeBearerType1Code | null>;
  // fieldA: FormControl<string>;
  // fieldB: FormControl<string>;
  // fieldC: FormControl<string>;
  // fieldD: FormControl<string>;
}>;

export const formSteps: FormStep[] = [
  {
    key: 'settlementInformation',
    controlNames: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd'],
    item: {
      label: 'Informationen zur Abwicklung der Zahlung (Settlement)',
    },
  },
  // {
  //   key: 'paymentBasics',
  //   controlNames: [
  //     'CdtTrfTxInf-PmtId-InstrId',
  //     'CdtTrfTxInf-PmtId-EndToEndId',
  //     'CdtTrfTxInf-PmtTpInf-InstrPrty',
  //     'CdtTrfTxInf-PmtTpInf-SvcLvl-Cd',
  //     'CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd',
  //     'CdtTrfTxInf-Purp-Cd',
  //     'CdtTrfTxInf-Purp-Prtry',
  //   ],
  //   item: {
  //     label: 'Payment Basics',
  //   },
  // },
  // {
  //   key: 'amountAndCurrency',
  //   controlNames: [
  //     'CdtTrfTxInf-InstdAmt',
  //     'CdtTrfTxInf-InstdAmt-Ccy',
  //     'CdtTrfTxInf-IntrBkSttlmAmt-Ccy',
  //     'CdtTrfTxInf-XchgRate',
  //     'CdtTrfTxInf-IntrBkSttlmAmt',
  //     'CdtTrfTxInf-ChrgBr',
  //     'CdtTrfTxInf-ChrgsInf-Amt',
  //     'CdtTrfTxInf-ChrgsInf-Amt-Ccy',
  //   ],
  //   item: {
  //     label: 'Amount & Currency Details',
  //   },
  // },
  // {
  //   key: 'miscellaneous',
  //   controlNames: ['fieldA', 'fieldB', 'fieldC', 'fieldD', 'fieldE', 'fieldF'],
  //   item: {
  //     label: 'Miscellaneous',
  //   },
  // },
  // {
  //   key: 'reviewAndSubmit',
  //   controlNames: [],
  //   item: {
  //     label: 'Review & Submit',
  //   },
  // },
];

// {
//   title: 'Settlement Information',
//   controlNames: [
//     'GrpHdr-SttlmInf-SttlmMtd',
//     'CdtTrfTxInf-IntrBkSttlmDt',
//     'CdtTrfTxInf-SttlmTmReq-FrTm',
//     'CdtTrfTxInf-SttlmTmReq-TillTm',
//     'CdtTrfTxInf-SttlmTmReq-CLSTm',
//     'CdtTrfTxInf-SttlmTmReq-RjctTm',
//   ],
// },
// {
//   title: 'Debtor Information',
//   controlNames: [
//     'CdtTrfTxInf-Dbtr-Nm',
//     'CdtTrfTxInf-Dbtr-PstlAdr-StrtNm',
//     'CdtTrfTxInf-Dbtr-PstlAdr-BldgNb',
//     'CdtTrfTxInf-Dbtr-PstlAdr-PstCd',
//     'CdtTrfTxInf-Dbtr-PstlAdr-TwnNm',
//     'CdtTrfTxInf-Dbtr-PstlAdr-Ctry',
//     'CdtTrfTxInf-Dbtr-PstlAdr-AdrLine',
//     'CdtTrfTxInf-DbtrAcct-Id-IBAN',
//     'CdtTrfTxInf-DbtrAcct-Ccy',
//     'CdtTrfTxInf-UltmtDbtr-Nm',
//   ],
// },
// {
//   title: 'Creditor Information',
//   controlNames: [
//     'CdtTrfTxInf-Cdtr-Nm',
//     'CdtTrfTxInf-Cdtr-PstlAdr-StrtNm',
//     'CdtTrfTxInf-Cdtr-PstlAdr-BldgNb',
//     'CdtTrfTxInf-Cdtr-PstlAdr-PstCd',
//     'CdtTrfTxInf-Cdtr-PstlAdr-TwnNm',
//     'CdtTrfTxInf-Cdtr-PstlAdr-Ctry',
//     'CdtTrfTxInf-Cdtr-PstlAdr-AdrLine',
//     'CdtTrfTxInf-CdtrAcct-Id-IBAN',
//     'CdtTrfTxInf-CdtrAcct-Ccy',
//     'CdtTrfTxInf-UltmtCdtr-Nm',
//   ],
// },
// {
//   title: 'Financial Institution Information',
//   controlNames: [
//     'CdtTrfTxInf-DbtrAgt-FinInstnId-BIC',
//     'CdtTrfTxInf-CdtrAgt-FinInstnId-BIC',
//     'GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI',
//     'GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI',
//   ],
// },
// {
//   title: 'Remittance Information',
//   controlNames: [
//     'CdtTrfTxInf-RmtInf-Ustrd',
//     'CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf',
//     'CdtTrfTxInf-RltdRmtInf-RmtId',
//     'CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd',
//     'CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr',
//   ],
// },
