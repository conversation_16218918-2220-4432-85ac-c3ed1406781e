<h2>PACS.008</h2>
<div class="pacs008-create">
  <!-- @if (userData$ | async; as user) {
  <div>
    <strong>User Data:</strong>
    <span>{{ user.name }}</span>
  </div>
  } @else {
  <div>Loading User Data ...</div>
  } -->

  <!-- Define toggles not bound into the form outside of the form as ngModel cannot be used to register form controls with a parent formGroup directive -->
  <!-- <ng-template #purposeToggle>
    <app-select-button
      [options]="useProprietaryPurposeOptions"
      [defaultValue]="false"
      (onChange)="onPurposeToggle($event)"
    />
  </ng-template>
  <ng-template #currencyConversionToggle>
    <app-radio-button-group
      [options]="useCurrencyConversionOptions"
      [defaultValue]="null"
      (onChange)="onCurrencyConversionToggle($event)"
    />
  </ng-template>
  <ng-template #conversionMethodToggle>
    <app-radio-button-group
      [options]="conversionMethodOptions"
      [defaultValue]="null"
      (onChange)="onConversionMethodToggle($event)"
    />
  </ng-template> -->

  <form
    [formGroup]="pacs008FormGroup"
    [formRules]="pacs008ValidationRules"
    (ngSubmit)="onSubmit()"
  >
    <app-form-stepper
      [steps]="formSteps"
      [currentStepIndex]="currentStepIndex()"
      (stepChange)="onStepChange($event)"
    >
      @switch (currentStepIndex()) { @case(0) {
      <app-form-radio-button-group
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"
        label="Settlement Method"
        [options]="settlementMethodOptions"
      />
      <h3>Instructing Reimbursement Agent</h3>
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"
        label="BICFI"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
        label="Clearing System Identification"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"
        label="Clearing System Member Identification"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI"
        label="Legal Entity Identifier (LEI)"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"
        label="Name"
      />
      <h4>Adresse</h4>
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"
        label="Department"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"
        label="SubDepartment"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"
        label="StreetName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"
        label="BuildingNumber"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"
        label="BuildingName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"
        label="Floor"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"
        label="PostBox"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"
        label="Room"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"
        label="PostCode"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"
        label="TownName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"
        label="TownLocationName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"
        label="DistrictName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"
        label="CountrySubDivision"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"
        label="Country"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        label="AddressLine"
      />
      <!-- <app-base-field
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        label="AddressLine"
        
      >
        <div
          formArrayName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        >
          @for (entry of
          pacs008FormGroup.controls["FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"].controls;
          track index; let index = $index) {
          <div>
            <input
              type="text"
              pInputText
              [id]="index"
              [formControlName]="index"
            />
            <span
              (click)="
                removeItem(
                  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine',
                  index
                )
              "
              >X</span
            >
          </div>
          }
        </div>
        <p-button
          type="button"
          label="Zeile hinzufügen"
          (onClick)="
            addItem(
              'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine'
            )
          "
        /> 
      </app-base-field>-->
      <h3>Instructed Reimbursement Agent</h3>
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"
        label="BICFI"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"
        label="Clearing System Identification"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"
        label="Clearing System Member Identification"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI"
        label="Legal Entity Identifier (LEI)"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"
        label="Name"
      />
      <h4>Adresse</h4>
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"
        label="Department"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"
        label="SubDepartment"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"
        label="StreetName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"
        label="BuildingNumber"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"
        label="BuildingName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"
        label="Floor"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"
        label="PostBox"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"
        label="Room"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"
        label="PostCode"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"
        label="TownName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"
        label="TownLocationName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"
        label="DistrictName"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"
        label="CountrySubDivision"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"
        label="Country"
      />
      <app-form-text-input
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        label="AddressLine"
      />
      <!-- <app-base-field
        fieldName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        label="AddressLine"
        
      >
        <div
          formArrayName="FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"
        >
          @for (entry of
          pacs008FormGroup.controls["FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"].controls;
          track index; let index = $index) {
          <div>
            <input
              type="text"
              pInputText
              [id]="index"
              [formControlName]="index"
            />
            <span
              (click)="
                removeItem(
                  'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine',
                  index
                )
              "
              >X</span
            >
          </div>
          }
        </div>
        <p-button
          type="button"
          label="Zeile hinzufügen"
          (onClick)="
            addItem(
              'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine'
            )
          "
        />
      </app-base-field> -->
      <!-- <app-form-text-input
        autohide="CdtTrfTxInf-PmtId-InstrId"
        fieldName="CdtTrfTxInf-PmtId-InstrId"
        label="Instruction ID"
      />
      <app-form-text-input
        fieldName="CdtTrfTxInf-PmtId-EndToEndId"
        label="End to End ID"
      />
      <app-form-radio-button-group
        fieldName="CdtTrfTxInf-PmtTpInf-InstrPrty"
        label="Payment Priority"
        [options]="paymentPriorityOptions"
      />
      <app-form-radio-button-group
        fieldName="CdtTrfTxInf-PmtTpInf-SvcLvl-Cd"
        label="Service Level"
        [options]="serviceLevelOptions"
      />
      <app-form-select
        fieldName="CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd"
        label="Category Purpose"
        [options]="categoryPurposeOptions"
        placeholder="Select Category Purpose"
      />
      <div class="purpose-select">
        <div class="purpose-select-title"><strong>Purpose</strong></div>
        <ng-container *ngTemplateOutlet="purposeToggle" />
        @if (useProprietaryPurpose()) {
        <app-form-text-input
          fieldName="CdtTrfTxInf-Purp-Prtry"
          label=""
        />
        } @else {
        <app-form-select
          fieldName="CdtTrfTxInf-Purp-Cd"
          label=""
          [options]="purposeOptions"
          placeholder="Select Purpose"
          [editable]="true"
        />
        }
      </div> -->
      } @case(1) {
      <!-- <div class="combined-input">
        <app-form-number-input
          fieldName="CdtTrfTxInf-InstdAmt"
          label="Instructed Amount"
          [maxFractionDigits]="2"
        />
        <app-form-select
          fieldName="CdtTrfTxInf-InstdAmt-Ccy"
          label="Currency"
          [options]="currencyOptions"
          placeholder="Select Currency"
          [filter]="true"
        />
      </div>
      <div>
        <p><strong>Does this payment involve currency conversion?</strong></p>
        <ng-container *ngTemplateOutlet="currencyConversionToggle" />
      </div>
      @if (useCurrencyConversion()) {
      <app-form-select
        fieldName="CdtTrfTxInf-IntrBkSttlmAmt-Ccy"
        label="Settlement Currency"
        [options]="currencyOptions"
        placeholder="Select Settlement Currency"
        [filter]="true"
      />
      <ng-container *ngTemplateOutlet="conversionMethodToggle" />
      @if (useExchangeRateConversionMethod() !== null) { @if
      (useExchangeRateConversionMethod()) {
      <div>
        <app-form-number-input
          fieldName="CdtTrfTxInf-XchgRate"
          label="Exchange Rate"
          [max]="100"
          [maxFractionDigits]="4"
          (onChange)="onExchangeRateChange($event)"
        />
        <p>
          Calculated Settlement Amount:
          {{ pacs008FormGroup.get("CdtTrfTxInf-IntrBkSttlmAmt")?.value ?? 0 }}
          {{
            pacs008FormGroup.get("CdtTrfTxInf-IntrBkSttlmAmt-Ccy")?.value ?? "-"
          }}
        </p>
      </div>

      } @else {
      <app-form-number-input
        fieldName="CdtTrfTxInf-IntrBkSttlmAmt"
        label="Interbank Settlement Amount"
        (onChange)="onInterbankSettlementAmountChange($event)"
        [maxFractionDigits]="2"
      />
      <p>
        Calculated Exchange Rate:
        {{ pacs008FormGroup.get("CdtTrfTxInf-XchgRate")?.value ?? "-" }}
      </p>
      }} }
      <app-form-select
        fieldName="CdtTrfTxInf-ChrgBr"
        label="Charge Bearer"
        [options]="chargeBearerOptions"
        placeholder="Select Charge Bearer"
      /> -->
      } @case(2) {
      <!-- <app-form-text-input
        fieldName="fieldA"
        label="Required Field"
      />
      <app-form-text-input
        fieldName="fieldB"
        label="Field with Max Length and Pattern Restriction"
        
      />
      <app-form-text-input
        fieldName="fieldC"
        label="Conditionally Visible Field"
      />
      <app-form-text-input
        fieldName="fieldD"
        label="Conditionally Required Field"
      /> -->
      }@case(3) { } }

      <p-button stepperSubmit type="submit" label="Submit" severity="success" />
    </app-form-stepper>
  </form>
</div>
