import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  output,
} from '@angular/core';
import { FormStep } from './form-stepper.types';
import { StepsModule } from 'primeng/steps';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-form-stepper',
  imports: [StepsModule, ButtonModule],
  templateUrl: './form-stepper.component.html',
  styleUrl: './form-stepper.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormStepperComponent {
  steps = input.required<FormStep[]>();
  currentStepIndex = input.required<number>();

  stepChange = output<number>();

  items = computed(() => {
    return this.steps().map((step) => step.item);
  });

  nextStep() {
    if (this.currentStepIndex() < this.steps().length - 1) {
      this.stepChange.emit(this.currentStepIndex() + 1);
    }
  }

  previousStep() {
    if (this.currentStepIndex() > 0) {
      this.stepChange.emit(this.currentStepIndex() - 1);
    }
  }
}
