<div class="form-stepper">
  <!-- Steps header -->
  <p-steps
    [model]="items()"
    [readonly]="true"
    [activeIndex]="currentStepIndex()"
    class="step-header"
  />

  <!-- Step content (provided by parent) -->
  <div class="step-content">
    <ng-content></ng-content>
  </div>

  <!-- Navigation buttons -->
  <div class="step-buttons">
    <div class="navigation-buttons">
      @if (currentStepIndex() > 0) {
      <p-button type="button" label="Back" (onClick)="previousStep()" />
      <!-- <button type="button" (click)="previousStep()">Back</button> -->

      } @if (currentStepIndex() < steps().length - 1) {
      <p-button type="button" label="Next" (onClick)="nextStep()" />
      <!-- <button type="button" (click)="nextStep()">Next</button> -->

      }
    </div>
    @if (currentStepIndex() === steps().length - 1) {
    <ng-content select="[stepperSubmit]"></ng-content>
    }
  </div>
</div>
