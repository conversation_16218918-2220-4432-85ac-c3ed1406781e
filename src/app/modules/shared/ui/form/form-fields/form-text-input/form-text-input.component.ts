import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  Control<PERSON>ontainer,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { InputTextModule } from 'primeng/inputtext';

@Component({
  selector: 'app-form-text-input',
  imports: [ReactiveFormsModule, BaseFieldComponent, InputTextModule],
  templateUrl: './form-text-input.component.html',
  styleUrl: './form-text-input.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormTextInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
}
