import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  Signal,
} from '@angular/core';
import {
  ControlContainer,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { FieldErrorsComponent } from '../../../../../../../../libs/iso20022-angular-lib/projects/iso20022-lib/error';

@Component({
  selector: 'app-base-field',
  imports: [ReactiveFormsModule, FieldErrorsComponent],
  templateUrl: './base-field.component.html',
  styleUrl: './base-field.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BaseFieldComponent {
  fieldName = input.required<string>();
  label = input.required<string>();

  controlContainer = inject(ControlContainer);

  get formGroup(): FormGroup {
    return this.controlContainer.control as FormGroup;
  }
}
