import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
  model,
  output,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SelectOption } from '../../form-select';

@Component({
  selector: 'app-radio-button-group',
  imports: [RadioButtonModule, FormsModule],
  templateUrl: './radio-button-group.component.html',
  styleUrl: './radio-button-group.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RadioButtonGroupComponent<T> {
  options = input.required<SelectOption<T>[]>();
  defaultValue = input<T | null>(null);
  onChange = output<T | null>();

  selectedOption = model<T | null>(null);

  constructor() {
    effect(() => {
      this.selectedOption.set(this.defaultValue());
    });
    effect(() => {
      this.onChange.emit(this.selectedOption());
    });
  }
}
