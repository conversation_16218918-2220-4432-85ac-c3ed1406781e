import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  Control<PERSON>ontainer,
  FormGroup,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SelectOption } from '../form-select';

@Component({
  selector: 'app-form-radio-button-group',
  imports: [ReactiveFormsModule, BaseFieldComponent, RadioButtonModule],
  templateUrl: './form-radio-button-group.component.html',
  styleUrl: './form-radio-button-group.component.css',
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree, required to use 'formControlName' within the component.
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormRadioButtonGroupComponent<T extends string> {
  fieldName = input.required<string>();
  label = input.required<string>();
  options = input.required<SelectOption<T>[]>();
}
