import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RadioButtonGroupComponent } from './radio-button-group.component';

describe('RadioButtonGroupComponent', () => {
  let component: RadioButtonGroupComponent<string>;
  let fixture: ComponentFixture<RadioButtonGroupComponent<string>>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RadioButtonGroupComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(RadioButtonGroupComponent<string>);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
