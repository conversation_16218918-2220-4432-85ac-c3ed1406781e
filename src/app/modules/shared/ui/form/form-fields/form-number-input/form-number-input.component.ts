import {
  ChangeDetectionStrategy,
  Component,
  input,
  output,
} from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { InputNumberInputEvent, InputNumberModule } from 'primeng/inputnumber';

@Component({
  selector: 'app-form-number-input',
  imports: [ReactiveFormsModule, BaseFieldComponent, InputNumberModule],
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree.
  ],
  templateUrl: './form-number-input.component.html',
  styleUrl: './form-number-input.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormNumberInputComponent {
  fieldName = input.required<string>();
  label = input.required<string>();
  max = input<number | null>(null);
  maxFractionDigits = input<number>(0);
  onChange = output<number | null>();

  onInput($event: InputNumberInputEvent) {
    const value = $event.value;
    this.onChange.emit(
      value === null
        ? null
        : typeof value === 'string'
        ? parseFloat(value)
        : value
    );
  }
}
