import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import {
  ControlContainer,
  FormGroupDirective,
  ReactiveFormsModule,
} from '@angular/forms';
import { BaseFieldComponent } from '../base-field';
import { SelectModule } from 'primeng/select';
import { SelectOption } from './form-select.types';

@Component({
  selector: 'app-form-select',
  imports: [ReactiveFormsModule, BaseFieldComponent, SelectModule],
  viewProviders: [
    { provide: ControlContainer, useExisting: FormGroupDirective }, // When resolving 'ControlContainer', just use the parent form that was declared up the tree.
  ],
  templateUrl: './form-select.component.html',
  styleUrl: './form-select.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormSelectComponent<T> {
  fieldName = input.required<string>();
  label = input.required<string>();
  options = input.required<SelectOption<T>[]>();
  placeholder = input.required<string>();
  editable = input<boolean>(false);
  filter = input<boolean>(false);
}
