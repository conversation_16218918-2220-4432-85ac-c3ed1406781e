import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

export const AppPreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{stone.50}',
      100: '{stone.100}',
      200: '{stone.200}',
      300: '{stone.300}',
      400: '{stone.400}',
      500: '{stone.500}',
      600: '{stone.600}',
      700: '{stone.700}',
      800: '{stone.800}',
      900: '{stone.900}',
      950: '{stone.950}',
    },
    colorScheme: {
      light: {
        surface: {
          0: '#ffffff',
          50: '{stone.50}',
          100: '{stone.100}',
          200: '{stone.200}',
          300: '{stone.300}',
          400: '{stone.400}',
          500: '{stone.500}',
          600: '{stone.600}',
          700: '{stone.700}',
          800: '{stone.800}',
          900: '{stone.900}',
          950: '{stone.950}',
        },
      },
    },
  },
  components: {
    steps: {
      colorScheme: {
        light: {
          item: {
            number: {
              active: {
                color: '{primary.950}',
                border: {
                  color: '{primary.950}',
                },
              },
            },
            label: {
              active: {
                color: '{primary.950}',
              },
            },
          },
        },
      },
    },
    // card: {
    //   colorScheme: {
    //       light: {
    //           root: {
    //               background: '{surface.0}',
    //               color: '{surface.700}'
    //           },
    //           subtitle: {
    //               color: '{surface.500}'
    //           }
    //       },
    //   }
    // }
  },
});
