{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"iso20022-lib": {"projectType": "library", "root": "projects/iso20022-lib", "sourceRoot": "projects/iso20022-lib/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/iso20022-lib/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/iso20022-lib/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/iso20022-lib/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/iso20022-lib/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}, "test-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/test-app", "sourceRoot": "projects/test-app/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/test-app", "index": "projects/test-app/src/index.html", "browser": "projects/test-app/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/test-app/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/test-app/public"}], "styles": ["projects/test-app/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "test-app:build:production"}, "development": {"buildTarget": "test-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "projects/test-app/tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/test-app/public"}], "styles": ["projects/test-app/src/styles.scss"], "scripts": []}}}}}}