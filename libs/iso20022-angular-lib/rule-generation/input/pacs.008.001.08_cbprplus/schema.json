{"$comment": {"legalNotices": "SWIFT SCRL@2023. All rights reserved.\n\nThis schema is a component of MyStandards, the SWIFT collaborative Web application used to manage\nstandards definitions and industry usage.\n\nThis is a licensed product, which may only be used and distributed in accordance with MyStandards License\nTerms as specified in MyStandards Service Description and the related Terms of Use.\n\nUnless otherwise agreed in writing with SWIFT SCRL, the user has no right to:\n - authorise external end users to use this component for other purposes than their internal use.\n - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.\n - re-sell or authorise another party e.g. software and service providers, to re-sell this component.\n\nThis component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties\nwith respect to this component such as but not limited to any guarantee as to its quality, supply or availability.\n\nAny and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual \nproperty rights of whatever nature in this component will remain the exclusive property of SWIFT or its \nlicensors.\n\nTrademarks\nSWIFT is the trade name of S.W.I.F.T. SCRL.\nThe following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.\nOther product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.", "group": "Cross Border Payments and Reporting Plus (CBPR+)", "collection": "CBPRPlus SR2025 (Combined)", "usageGuideline": "CBPRPlus-pacs.008.001.08_FIToFICustomerCreditTransfer", "baseMessage": "pacs.008.001.08", "dateOfPublication": "17 March 2025", "url": "https://www2.swift.com/mystandards/#/mp/mx/_q0jt4JpiEe6MIJTGjiktfA/_q0wiMZpiEe6MIJTGjiktfA", "description": "Principles:\r\n\r\n1A. AGENTS IDENTIFICATION -Textual Rules\r\n\r\n-> If BICFI is present, then (Name & Postal Address) is NOT allowed (ClearingSystemMemberIdentification and LEI may complement) – However, in case of conflicting information, the BICFI will always take precedence.\r\n\r\n-> If BICFI is absent, (Name & Postal Address) OR [(Name & Postal Address) and ClearingSystemMemberIdentification] must be present.\r\nException: If BICFI is absent, whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code only may be used.\r\n\r\nNote: \"Instructing/ Instructed Agents\" must be identified with a BICFI - Clearing System Members Identification and LEI are optional.\r\n\r\n1B. DEBTOR/CREDITOR - PARTY IDENTIFICATION - Textual Rules\r\n\r\n-> If AnyBIC is present, then (Name and Postal Address) is NOT allowed (other elements remain optional) - However, in case of conflicting information, AnyBIC will always take precedence.\r\n\r\n-> If Name is present, it is recommended to use Postal Address.\r\n\r\n\r\n2. Single transactions only are allowed.\r\n\r\n\r\n3. Character Set:\r\n\r\nAll proprietary and Text fields EXCLUDING Name and Address for all actors and Related Remittance Information and Remittance are limited to the FIN-X-Character set.\r\n\r\nAll Name and Address for all actors, Related Remittance Information and Remittance Information (structured and unstructured), Email Address where included as part of a proxy element are extended to support the following additional characters:\r\n\r\n  !#$&%*=^_’{|}~\";<>@[\\]\r\n\r\n< is replaced with &lt;\r\n> is replaced with &gt;\r\n\r\n\r\n4. CBPR_Agent_PointToPointOnSWIFT:\r\n\r\nIf the transaction is exchanged on the SWIFT network (ie if the instructing agent/sender and instruted agent/receiver of the message are on SWIFT), then BICFI is mandatory and other elements are optional, eg LEI\r\n\r\n"}, "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "description": "ISO 20022 JSON Schema FIToFICustomerCreditTransferV08 (pacs.008.001.08) Generated by SWIFT MyStandards 2025-04-18 11:49:27", "additionalProperties": false, "properties": {"$id": {"default": "urn:iso:std:iso:20022:tech:json:pacs.008.001.08"}, "fi_to_fi_customer_credit_transfer_v08": {"$ref": "#/definitions/FIToFICustomerCreditTransferV08"}}, "definitions": {"AccountIdentification4Choice__1": {"type": "object", "description": "Specifies the unique identification of an account as assigned by the account servicer.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"iban": {"description": "International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 \"Banking and related financial services - International Bank Account Number (IBAN)\" version 1997-10-01, or later revisions.", "$ref": "#/definitions/IBAN2007Identifier"}}, "required": ["iban"]}, {"type": "object", "additionalProperties": false, "properties": {"other": {"description": "Unique identification of an account, as assigned by the account servicer, using an identification scheme.", "$ref": "#/definitions/GenericAccountIdentification1__1"}}, "required": ["other"]}]}, "AccountSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalAccountIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ActiveCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\"."}, "ActiveOrHistoricCurrencyAndAmount": {"type": "object", "description": "A number of monetary units specified in an active or a historic currency where the unit of currency is explicit and compliant with ISO 4217.", "additionalProperties": false, "properties": {"currency": {"$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "amount": {"type": "string", "maxLength": 19, "pattern": "^0*(([0-9]{0,13}\\.[0-9]{1,5})|([0-9]{0,14}\\.[0-9]{1,4})|([0-9]{0,15}\\.[0-9]{1,3})|([0-9]{0,16}\\.[0-9]{1,2})|([0-9]{0,17}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,18}))$"}}, "required": ["currency", "amount"]}, "ActiveOrHistoricCurrencyCode": {"type": "string", "pattern": "^[A-Z]{3,3}$", "description": "A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 \"Codes for the representation of currencies and funds\"."}, "AnyBICDec2014Identifier": {"type": "string", "description": "Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BICFIDec2014Identifier": {"type": "string", "description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "pattern": "^[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}$"}, "BaseOneRate": {"type": "string", "description": "Rate expressed as a decimal, for example, 0.7 is 7/10 and 70%.", "maxLength": 12}, "BranchAndFinancialInstitutionIdentification6__1": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__2": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__2"}}, "required": ["financial_institution_identification"]}, "BranchAndFinancialInstitutionIdentification6__3": {"type": "object", "description": "Unique and unambiguous identification of a financial institution or a branch of a financial institution.", "additionalProperties": false, "properties": {"financial_institution_identification": {"description": "Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.", "$ref": "#/definitions/FinancialInstitutionIdentification18__1"}, "branch_identification": {"description": "Identifies a specific branch of a financial institution.  Usage: This component should be used in case the identification information in the financial institution component does not provide identification up to branch level.", "$ref": "#/definitions/BranchData3__1"}}, "required": ["financial_institution_identification"]}, "BranchData3__1": {"type": "object", "description": "Information that locates and identifies a specific branch of a financial institution.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a branch of a financial institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}}, "CBPR_Amount__1": {"type": "object", "additionalProperties": false, "properties": {"currency": {"$ref": "#/definitions/ActiveCurrencyCode"}, "amount": {"type": "string", "maxLength": 15, "pattern": "^0*(([0-9]{0,9}\\.[0-9]{1,5})|([0-9]{0,10}\\.[0-9]{1,4})|([0-9]{0,11}\\.[0-9]{1,3})|([0-9]{0,12}\\.[0-9]{1,2})|([0-9]{0,13}\\.[0-9]{1,1})|([0-9]{0,18}\\.)|0*|([0-9]{0,14}))$"}}, "required": ["currency", "amount"]}, "CBPR_DateTime": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm). \r\n\r\nThis representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 2020-07-16T19:20:30.45+01:00", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)T(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d(?:\\.[0-9]+)?(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "CBPR_RestrictedFINXMax10Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 10 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 10, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax140Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax140Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 140 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 140, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax16Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax16Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 16 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 16, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax2048Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 2048 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 2048, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax28Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 28 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 28, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax320Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 320 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]\r\n", "minLength": 1, "maxLength": 320, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax34Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 34 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . and disable the use of slash \"/\" at the beginning and end of line and double slash \"//\" within the line.", "minLength": 1, "maxLength": 34, "pattern": "^([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]([0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ]*(/[0-9a-zA-Z\\-\\?:\\(\\)\\.,'\\+ ])?)*)$"}, "CBPR_RestrictedFINXMax35Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ‘ + .", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax35Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 35 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 35, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax4Text_Extended": {"type": "string", "description": "Specifies a character string witha minimum length of 1, and a maximum length of 4 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 4, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_RestrictedFINXMax70Text": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + .", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ ]+$"}, "CBPR_RestrictedFINXMax70Text_Extended": {"type": "string", "description": "Specifies a character string with a minimum length of 1, and a maximum length of 70 characters, and limited to character set X, that is, 0-9 a-z A-Z / - ? : ( ) . , ' + . that is extended to support the following additional characters !#$%&*=^_`{|}~\";<>@[\\]", "minLength": 1, "maxLength": 70, "pattern": "^[0-9a-zA-Z/\\-\\?:\\(\\)\\.,'\\+ !#$%&\\*=^_`\\{\\|\\}~\";<>@\\[\\\\\\]]+$"}, "CBPR_Time": {"type": "string", "description": "A particular point in the progression of time defined by a mandatory time component, expressed in local time with UTC offset format (hh:mm:ss.sss+/-hh:mm). \r\n\r\nNote on the time format:\r\n1) beginning / end of calendar day\r\n00:00:00 = the beginning of a calendar day\r\n24:00:00 = the end of a calendar day\r\n2) fractions of second in time format\r\nDecimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.\r\n\r\nExample: 19:20:30.45+01:00", "pattern": "^(?:[01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d((?:\\.[0-9]+)?)?(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "CashAccount38__1": {"type": "object", "description": "Provides the details to identify an account.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification for the account between the account owner and the account servicer.", "$ref": "#/definitions/AccountIdentification4Choice__1"}, "type": {"description": "Specifies the nature, or use of the account.", "$ref": "#/definitions/CashAccountType2Choice__1"}, "currency": {"description": "Identification of the currency in which the account is held.   Usage: Currency should only be used in case one and the same account number covers several currencies and the initiating party needs to identify which currency needs to be used for settlement on the account.", "$ref": "#/definitions/ActiveOrHistoricCurrencyCode"}, "name": {"description": "Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.  Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text"}, "proxy": {"description": "Specifies an alternate assumed name for the identification of the account. ", "$ref": "#/definitions/ProxyAccountIdentification1__1"}}, "required": ["identification"]}, "CashAccountType2Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Account type, in a coded form.", "$ref": "#/definitions/ExternalCashAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Nature or use of the account in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "CategoryPurpose1Choice__1": {"type": "object", "description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories.\nUsage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Category purpose, as published in an external category purpose code list.", "$ref": "#/definitions/ExternalCategoryPurpose1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Category purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ChargeBearerType1Code__1": {"type": "string", "description": "Specifies which party(ies) will pay charges due for processing of the instruction.\n*`DEBT`-All transaction charges are to be borne by the debtor.\n*`CRED`-All transaction charges are to be borne by the creditor.\n*`SHAR`-In a credit transfer context, means that transaction charges on the sender side are to be borne by the debtor, transaction charges on the receiver side are to be borne by the creditor. In a direct debit context, means that transaction charges on the sender side are to be borne by the creditor, transaction charges on the receiver side are to be borne by the debtor.", "enum": ["DEBT", "CRED", "SHAR"]}, "Charges7__1": {"type": "object", "description": "Provides information on the charges related to the payment transaction.", "additionalProperties": false, "properties": {"amount": {"description": "Transaction charges to be paid by the charge bearer.", "$ref": "#/definitions/CBPR_Amount__1"}, "agent": {"description": "Agent that takes the transaction charges or to which the transaction charges are due.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}}, "required": ["amount", "agent"]}, "ClearingChannel2Code": {"type": "string", "description": "Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.\n*`RTGS`-Clearing channel is a real-time gross settlement system.\n*`RTNS`-Clearing channel is a real-time net settlement system.\n*`MPNS`-Clearing channel is a mass payment net settlement system.\n*`BOOK`-Payment through internal book transfer.", "enum": ["RTGS", "RTNS", "MPNS", "BOOK"]}, "ClearingSystemIdentification2Choice__1": {"type": "object", "description": "Choice of a clearing system identifier.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Identification of a clearing system, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalClearingSystemIdentification1Code"}}, "required": ["code"]}]}, "ClearingSystemMemberIdentification2__1": {"type": "object", "description": "Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.", "additionalProperties": false, "properties": {"clearing_system_identification": {"description": "Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.", "$ref": "#/definitions/ClearingSystemIdentification2Choice__1"}, "member_identification": {"description": "Identification of a member of a clearing system.", "$ref": "#/definitions/CBPR_RestrictedFINXMax28Text"}}, "required": ["clearing_system_identification", "member_identification"]}, "CountryCode": {"type": "string", "pattern": "^[A-Z]{2,2}$", "description": "Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code)."}, "CreditDebitCode": {"type": "string", "description": "Specifies if an operation is an increase or a decrease.\n*`CRDT`-Operation is an increase.\n*`DBIT`-Operation is a decrease.", "enum": ["CRDT", "DBIT"]}, "CreditTransferTransaction39__1": {"type": "object", "description": "Provides further details specific to the individual transaction(s) included in the message.", "additionalProperties": false, "properties": {"payment_identification": {"description": "Set of elements used to reference a payment instruction.", "$ref": "#/definitions/PaymentIdentification7__1"}, "payment_type_information": {"description": "Set of elements used to further specify the type of transaction.", "$ref": "#/definitions/PaymentTypeInformation28__1"}, "interbank_settlement_amount": {"description": "Amount of money moved between the instructing agent and the instructed agent.", "$ref": "#/definitions/CBPR_Amount__1"}, "interbank_settlement_date": {"description": "Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.", "$ref": "#/definitions/ISODate"}, "settlement_priority": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the settlement instruction.", "$ref": "#/definitions/Priority3Code"}, "settlement_time_indication": {"description": "Provides information on the occurred settlement time(s) of the payment transaction.", "$ref": "#/definitions/SettlementDateTimeIndication1__1"}, "settlement_time_request": {"description": "Provides information on the requested settlement time(s) of the payment instruction.", "$ref": "#/definitions/SettlementTimeRequest2__1"}, "instructed_amount": {"description": "Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party. Usage: This amount has to be transported unchanged through the transaction chain.", "$ref": "#/definitions/CBPR_Amount__1"}, "exchange_rate": {"description": "Factor used to convert an amount from one currency into another. This reflects the price at which one currency was bought with another currency.", "$ref": "#/definitions/BaseOneRate"}, "charge_bearer": {"description": "Specifies which party/parties will bear the charges associated with the processing of the payment transaction.", "$ref": "#/definitions/ChargeBearerType1Code__1"}, "charges_information": {"type": "array", "description": "Provides information on the charges to be paid by the charge bearer(s) related to the payment transaction.", "items": {"$ref": "#/definitions/Charges7__1"}}, "previous_instructing_agent1": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent1_account": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "previous_instructing_agent2": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent2_account": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "previous_instructing_agent3": {"description": "Agent immediately prior to the instructing agent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "previous_instructing_agent3_account": {"description": "Unambiguous identification of the account of the previous instructing agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "instructing_agent": {"description": "Agent that instructs the next party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2"}, "instructed_agent": {"description": "Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__2"}, "intermediary_agent1": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than one intermediary agent is present, then IntermediaryAgent1 identifies the agent between the DebtorAgent and the IntermediaryAgent2.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent1_account": {"description": "Unambiguous identification of the account of the intermediary agent 1 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "intermediary_agent2": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If more than two intermediary agents are present, then IntermediaryAgent2 identifies the agent between the IntermediaryAgent1 and the IntermediaryAgent3.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent2_account": {"description": "Unambiguous identification of the account of the intermediary agent 2 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "intermediary_agent3": {"description": "Agent between the debtor's agent and the creditor's agent.  Usage: If IntermediaryAgent3 is present, then it identifies the agent between the IntermediaryAgent 2 and the CreditorAgent.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "intermediary_agent3_account": {"description": "Unambiguous identification of the account of the intermediary agent 3 at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "ultimate_debtor": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/PartyIdentification135__1"}, "initiating_party": {"description": "Party that initiates the payment. Usage: This can be either the debtor or a party that initiates the credit transfer on behalf of the debtor.", "$ref": "#/definitions/PartyIdentification135__1"}, "debtor": {"description": "Party that owes an amount of money to the (ultimate) creditor.", "$ref": "#/definitions/PartyIdentification135__2"}, "debtor_account": {"description": "Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1"}, "debtor_agent": {"description": "Financial institution servicing an account for the debtor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "debtor_agent_account": {"description": "Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "creditor_agent": {"description": "Financial institution servicing an account for the creditor.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__3"}, "creditor_agent_account": {"description": "Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1"}, "creditor": {"description": "Party to which an amount of money is due.", "$ref": "#/definitions/PartyIdentification135__3"}, "creditor_account": {"description": "Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.", "$ref": "#/definitions/CashAccount38__1"}, "ultimate_creditor": {"description": "Ultimate party to which an amount of money is due.", "$ref": "#/definitions/PartyIdentification135__1"}, "instruction_for_creditor_agent": {"type": "array", "maxItems": 2, "description": "Further information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor agent.", "items": {"$ref": "#/definitions/InstructionForCreditorAgent1__1"}}, "instruction_for_next_agent": {"type": "array", "maxItems": 6, "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the next agent.   Usage: The next agent may not be the creditor agent. The instruction can relate to a level of service, can be an instruction that has to be executed by the agent, or can be information required by the next agent.", "items": {"$ref": "#/definitions/InstructionForNextAgent1__1"}}, "purpose": {"description": "Underlying reason for the payment transaction. Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/Purpose2Choice__1"}, "regulatory_reporting": {"type": "array", "maxItems": 10, "description": "Information needed due to regulatory and statutory requirements.", "items": {"$ref": "#/definitions/RegulatoryReporting3__1"}}, "related_remittance_information": {"description": "Provides information related to the handling of the remittance information by any of the agents in the transaction processing chain.", "$ref": "#/definitions/RemittanceLocation7__1"}, "remittance_information": {"description": "Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.", "$ref": "#/definitions/RemittanceInformation16__1"}}, "required": ["payment_identification", "interbank_settlement_amount", "interbank_settlement_date", "charge_bearer", "instructing_agent", "instructed_agent", "debtor", "debtor_agent", "creditor_agent", "creditor"]}, "CreditorReferenceInformation2__1": {"type": "object", "description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of creditor reference.", "$ref": "#/definitions/CreditorReferenceType2__1"}, "reference": {"description": "Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.  Usage: If available, the initiating party should provide this reference in the structured remittance information, to enable reconciliation by the creditor upon receipt of the amount of money.  If the business context requires the use of a creditor reference or a payment remit identification, and only one identifier can be passed through the end-to-end chain, the creditor's reference or payment remittance identification should be quoted in the end-to-end transaction identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}}, "CreditorReferenceType1Choice__1": {"type": "object", "description": "Specifies the type of document referred by the creditor.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Type of creditor reference, in a coded form.", "$ref": "#/definitions/DocumentType3Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Creditor reference type, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "CreditorReferenceType2__1": {"type": "object", "description": "Specifies the type of creditor reference.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Coded or proprietary format creditor reference type.", "$ref": "#/definitions/CreditorReferenceType1Choice__1"}, "issuer": {"description": "Entity that assigns the credit reference type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "DateAndPlaceOfBirth1__1": {"type": "object", "description": "Date and place of birth of a person.", "additionalProperties": false, "properties": {"birth_date": {"description": "Date on which a person is born.", "$ref": "#/definitions/ISODate"}, "province_of_birth": {"description": "Province where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "city_of_birth": {"description": "City where a person was born.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_of_birth": {"description": "Country where a person was born.", "$ref": "#/definitions/CountryCode"}}, "required": ["birth_date", "city_of_birth", "country_of_birth"]}, "DatePeriod2": {"type": "object", "description": "Range of time defined by a start date and an end date.", "additionalProperties": false, "properties": {"from_date": {"description": "Start date of the range.", "$ref": "#/definitions/ISODate"}, "to_date": {"description": "End date of the range.", "$ref": "#/definitions/ISODate"}}, "required": ["from_date", "to_date"]}, "DiscountAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/DiscountAmountType1Choice__1"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "DiscountAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalDiscountAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "DocumentAdjustment1__1": {"type": "object", "description": "Set of elements used to provide information on the amount and reason of the document adjustment.", "additionalProperties": false, "properties": {"amount": {"description": "Amount of money of the document adjustment.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "credit_debit_indicator": {"description": "Specifies whether the adjustment must be subtracted or added to the total amount.", "$ref": "#/definitions/CreditDebitCode"}, "reason": {"description": "Specifies the reason for the adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax4Text_Extended"}, "additional_information": {"description": "Provides further details on the document adjustment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}, "required": ["amount"]}, "DocumentLineIdentification1__1": {"type": "object", "description": "Identifies the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of referred document line identification.", "$ref": "#/definitions/DocumentLineType1__1"}, "number": {"description": "Identification of the type specified for the referred document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "related_date": {"description": "Date associated with the referred document line.", "$ref": "#/definitions/ISODate"}}}, "DocumentLineInformation1__1": {"type": "object", "description": "Provides document line information.\r\n", "additionalProperties": false, "properties": {"identification": {"type": "array", "description": "Provides identification of the document line.", "items": {"$ref": "#/definitions/DocumentLineIdentification1__1"}}, "description": {"description": "Description associated with the document line.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "amount": {"description": "Provides details on the amounts of the document line.", "$ref": "#/definitions/RemittanceAmount3__1"}}, "required": ["identification"]}, "DocumentLineType1Choice__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Line identification type in a coded form.", "$ref": "#/definitions/ExternalDocumentLineType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "DocumentLineType1__1": {"type": "object", "description": "Specifies the type of the document line identification.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the referred document line identification.", "$ref": "#/definitions/DocumentLineType1Choice__1"}, "issuer": {"description": "Identification of the issuer of the reference document line identificationtype.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "DocumentType3Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`RADM`-Document is a remittance advice sent separately from the current transaction.\n*`RPIN`-Document is a linked payment instruction to which the current payment instruction is related, for example, in a cover scenario.\n*`FXDR`-Document is a pre-agreed or pre-arranged foreign exchange transaction to which the payment transaction refers.\n*`DISP`-Document is a dispatch advice.\n*`PUOR`-Document is a purchase order.\n*`SCOR`-Document is a structured communication reference provided by the creditor to identify the referred transaction.", "enum": ["RADM", "RPIN", "FXDR", "DISP", "PUOR", "SCOR"]}, "DocumentType6Code": {"type": "string", "description": "Specifies a type of financial or commercial document.\n*`MSIN`-Document is an invoice claiming payment for the supply of metered services, for example gas or electricity supplied to a fixed meter.\n*`CNFA`-Document is a credit note for the final amount settled for a commercial transaction.\n*`DNFA`-Document is a debit note for the final amount settled for a commercial transaction.\n*`CINV`-Document is an invoice.\n*`CREN`-Document is a credit note.\n*`DEBN`-Document is a debit note.\n*`HIRI`-Document is an invoice for the hiring of human resources or renting goods or equipment.\n*`SBIN`-Document is an invoice issued by the debtor.\n*`CMCN`-Document is an agreement between the parties, stipulating the terms and conditions of the delivery of goods or services.\n*`SOAC`-Document is a statement of the transactions posted to the debtor's account at the supplier.\n*`DISP`-Document is a dispatch advice.\n*`BOLD`-Document is a shipping notice.\n*`VCHR`-Document is an electronic payment document.\n*`AROI`-Document is a payment that applies to a specific source document.\n*`TSUT`-Document is a transaction identifier as assigned by the Trade Services Utility.\n*`PUOR`-Document is a purchase order.", "enum": ["MSIN", "CNFA", "DNFA", "CINV", "CREN", "DEBN", "HIRI", "SBIN", "CMCN", "SOAC", "DISP", "BOLD", "VCHR", "AROI", "TSUT", "PUOR"]}, "ExternalAccountIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalCashAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalCategoryPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the category purpose, as published in an external category purpose code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalClearingSystemIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 5, "description": "Specifies the clearing system identification code, as published in an external clearing system identification code list.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalDiscountAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalDocumentLineType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the document line type as published in an external document type code list."}, "ExternalGarnishmentType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the garnishment type as published in an external document type code list."}, "ExternalLocalInstrument1Code": {"type": "string", "minLength": 1, "maxLength": 35, "description": "Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalOrganisationIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPersonIdentification1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalProxyAccountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external proxy account type code, as published in the proxy account type external code set.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalPurpose1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external purpose code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalServiceLevel1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the external service level code in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "ExternalTaxAmountType1Code": {"type": "string", "minLength": 1, "maxLength": 4, "description": "Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.\r\nThe list of valid codes is an external code list published separately.\r\nExternal code sets can be downloaded from www.iso20022.org."}, "FIToFICustomerCreditTransferV08": {"type": "object", "description": "Scope\r\nThe FinancialInstitutionToFinancialInstitutionCustomerCreditTransfer message is sent by the debtor agent to the creditor agent, directly or through other agents and/or a payment clearing and settlement system. It is used to move funds from a debtor account to a creditor.\r\nUsage\r\nThe FIToFICustomerCreditTransfer message is exchanged between agents and can contain one or more customer credit transfer instructions.\r\nThe FIToFICustomerCreditTransfer message does not allow for grouping: a CreditTransferTransactionInformation block must be present for each credit transfer transaction.\r\nThe FIToFICustomerCreditTransfer message can be used in different ways:\r\n- If the instructing agent and the instructed agent wish to use their direct account relationship in the currency of the transfer then the message contains both the funds for the customer transfer(s) as well as the payment details;\r\n- If the instructing agent and the instructed agent have no direct account relationship in the currency of the transfer, or do not wish to use their account relationship, then other (reimbursement) agents will be involved to cover for the customer transfer(s). The FIToFICustomerCreditTransfer contains only the payment details and the instructing agent must cover the customer transfer by sending a FinancialInstitutionCreditTransfer to a reimbursement agent. This payment method is called the Cover method;\r\n- If more than two financial institutions are involved in the payment chain and if the FIToFICustomerCreditTransfer is sent from one financial institution to the next financial institution in the payment chain, then the payment method is called the Serial method.\r\nThe FIToFICustomerCreditTransfer message can be used in domestic and cross-border scenarios.", "additionalProperties": false, "properties": {"group_header": {"description": "Set of characteristics shared by all individual transactions included in the message.", "$ref": "#/definitions/GroupHeader93__1"}, "credit_transfer_transaction_information": {"description": "Set of elements providing information specific to the individual credit transfer(s).", "$ref": "#/definitions/CreditTransferTransaction39__1"}}, "required": ["group_header", "credit_transfer_transaction_information"]}, "FinancialInstitutionIdentification18__1": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}, "name": {"description": "Name by which an agent is known and which is usually used to identify that agent.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}}}, "FinancialInstitutionIdentification18__2": {"type": "object", "description": "Specifies the details to identify a financial institution.", "additionalProperties": false, "properties": {"bicfi": {"description": "Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 \"Banking - Banking telecommunication messages - Business identifier code (BIC)\".", "$ref": "#/definitions/BICFIDec2014Identifier"}, "clearing_system_member_identification": {"description": "Information used to identify a member within a clearing system.", "$ref": "#/definitions/ClearingSystemMemberIdentification2__1"}, "lei": {"description": "Legal entity identifier of the financial institution.", "$ref": "#/definitions/LEIIdentifier"}}, "required": ["bicfi"]}, "Garnishment3__1": {"type": "object", "description": "Provides remittance information about a payment for garnishment-related purposes.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of garnishment.", "$ref": "#/definitions/GarnishmentType1__1"}, "garnishee": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the garnisher.", "$ref": "#/definitions/PartyIdentification135__4"}, "garnishment_administrator": {"description": "Party on the credit side of the transaction who administers the garnishment on behalf of the ultimate beneficiary.", "$ref": "#/definitions/PartyIdentification135__4"}, "reference_number": {"description": "Reference information that is specific to the agency receiving the garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "date": {"description": "Date of payment which garnishment was taken from.", "$ref": "#/definitions/ISODate"}, "remitted_amount": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "family_medical_insurance_indicator": {"description": "Indicates if the person to whom the garnishment applies (that is, the ultimate debtor) has family medical insurance coverage available.", "$ref": "#/definitions/TrueFalseIndicator"}, "employee_termination_indicator": {"description": "Indicates if the employment of the person to whom the garnishment applies (that is, the ultimate debtor) has been terminated.", "$ref": "#/definitions/TrueFalseIndicator"}}, "required": ["type"]}, "GarnishmentType1Choice__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Garnishment type in a coded form. Would suggest this to be an External Code List to contain: GNCS    Garnishment from a third party payer for Child Support GNDP    Garnishment from a Direct Payer for Child Support GTPP     Garnishment from a third party payer to taxing agency.", "$ref": "#/definitions/ExternalGarnishmentType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of garnishment.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "GarnishmentType1__1": {"type": "object", "description": "Specifies the type of garnishment.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the garnishment.", "$ref": "#/definitions/GarnishmentType1Choice__1"}, "issuer": {"description": "Identification of the issuer of the garnishment type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "GenericAccountIdentification1__1": {"type": "object", "description": "Information related to a generic account identification.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax34Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/AccountSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericOrganisationIdentification1__1": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericOrganisationIdentification1__2": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__2"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification", "scheme_name"]}, "GenericOrganisationIdentification1__3": {"type": "object", "description": "Information related to an identification of an organisation.", "additionalProperties": false, "properties": {"identification": {"description": "Identification assigned by an institution.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/OrganisationIdentificationSchemeName1Choice__3"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["identification"]}, "GenericPersonIdentification1__1": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__1"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification"]}, "GenericPersonIdentification1__2": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__2"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["identification", "scheme_name"]}, "GenericPersonIdentification1__3": {"type": "object", "description": "Information related to an identification of a person.", "additionalProperties": false, "properties": {"identification": {"description": "Unique and unambiguous identification of a person.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "scheme_name": {"description": "Name of the identification scheme.", "$ref": "#/definitions/PersonIdentificationSchemeName1Choice__3"}, "issuer": {"description": "Entity that assigns the identification.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["identification"]}, "GroupHeader93__1": {"type": "object", "description": "Set of characteristics shared by all individual transactions included in the message.", "additionalProperties": false, "properties": {"message_identification": {"description": "Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message. Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "creation_date_time": {"description": "Date and time at which the message was created.", "$ref": "#/definitions/CBPR_DateTime"}, "number_of_transactions": {"description": "Number of individual transactions contained in the message.", "$ref": "#/definitions/Max15NumericText_fixed"}, "settlement_information": {"description": "Specifies the details on how the settlement of the transaction(s) between the instructing agent and the instructed agent is completed.", "$ref": "#/definitions/SettlementInstruction7__1"}}, "required": ["message_identification", "creation_date_time", "number_of_transactions", "settlement_information"]}, "IBAN2007Identifier": {"type": "string", "description": "The International Bank Account Number is a code used internationally by financial institutions to uniquely identify the account of a customer at a financial institution as described in the 2007 edition of the ISO 13616 standard \"Banking and related financial services - International Bank Account Number (IBAN)\" and replaced by the more recent edition of the standard.", "pattern": "^[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}$"}, "ISODate": {"type": "string", "description": "A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in \"XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004\" which is aligned with ISO 8601.", "pattern": "^(?:[1-9]\\d{3}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1\\d|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[1-9]\\d(?:0[48]|[2468][048]|[13579][26])|(?:[2468][048]|[13579][26])00)-02-29)(?:Z|[+-][01]\\d:[0-5]\\d)?$"}, "Instruction3Code": {"type": "string", "description": "Specifies further instructions concerning the processing of a payment instruction, provided by the sending clearing agent to the next agent(s).\n*`CHQB`-(Ultimate) creditor must be paid by cheque.\n*`HOLD`-Amount of money must be held for the (ultimate) creditor, who will call. Pay on identification.\n*`PHOB`-Please advise/contact (ultimate) creditor/claimant by phone.\n*`TELB`-Please advise/contact (ultimate) creditor/claimant by the most efficient means of telecommunication.", "enum": ["CHQB", "HOLD", "PHOB", "TELB"]}, "InstructionForCreditorAgent1__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by the creditor's agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the creditor's agent.", "additionalProperties": false, "properties": {"code": {"description": "Coded information related to the processing of the payment instruction, provided by the initiating party, and intended for the creditor's agent.", "$ref": "#/definitions/Instruction3Code"}, "instruction_information": {"description": "Further information complementing the coded instruction or instruction to the creditor's agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text"}}}, "InstructionForNextAgent1__1": {"type": "object", "description": "Further information related to the processing of the payment instruction that may need to be acted upon by an other agent. The instruction may relate to a level of service, or may be an instruction that has to be executed by the creditor's agent, or may be information required by the other agent.", "additionalProperties": false, "properties": {"instruction_information": {"description": "Further information complementing the coded instruction or instruction to the next agent that is bilaterally agreed or specific to a user community.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}}, "LEIIdentifier": {"type": "string", "description": "Legal Entity Identifier is a code allocated to a party as described in ISO 17442 \"Financial Services - Legal Entity Identifier (LEI)\".", "pattern": "^[A-Z0-9]{18,18}[0-9]{2,2}$"}, "LocalInstrument2Choice__1": {"type": "object", "description": "Set of elements that further identifies the type of local instruments being requested by the initiating party.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the local instrument, as published in an external local instrument code list.", "$ref": "#/definitions/ExternalLocalInstrument1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the local instrument, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "Max15NumericText_fixed": {"type": "string", "description": "\n*`1`-null", "enum": ["1"]}, "NameAndAddress16__1": {"type": "object", "description": "Information that locates and identifies a party.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "address": {"description": "Postal address of a party.", "$ref": "#/definitions/PostalAddress24__1"}}, "required": ["name", "address"]}, "Number": {"type": "string", "description": "Number of objects represented as an integer.", "maxLength": 19}, "OrganisationIdentification29__1": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__1"}}}}, "OrganisationIdentification29__2": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__2"}}}}, "OrganisationIdentification29__3": {"type": "object", "description": "Unique and unambiguous way to identify an organisation.", "additionalProperties": false, "properties": {"any_bic": {"description": "Business identification code of the organisation.", "$ref": "#/definitions/AnyBICDec2014Identifier"}, "lei": {"description": "Legal entity identification as an alternate identification for a party.", "$ref": "#/definitions/LEIIdentifier"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of an organisation, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericOrganisationIdentification1__3"}}}}, "OrganisationIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "OrganisationIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}]}, "OrganisationIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the organisation identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalOrganisationIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "Party38Choice__1": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__1"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__1"}}, "required": ["private_identification"]}]}, "Party38Choice__2": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__2"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__2"}}, "required": ["private_identification"]}]}, "Party38Choice__3": {"type": "object", "description": "Nature or use of the account.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"organisation_identification": {"description": "Unique and unambiguous way to identify an organisation.", "$ref": "#/definitions/OrganisationIdentification29__3"}}, "required": ["organisation_identification"]}, {"type": "object", "additionalProperties": false, "properties": {"private_identification": {"description": "Unique and unambiguous identification of a person, for example a passport.", "$ref": "#/definitions/PersonIdentification13__3"}}, "required": ["private_identification"]}]}, "PartyIdentification135__1": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__2": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__2"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__3": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__1"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__1"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PartyIdentification135__4": {"type": "object", "description": "Specifies the identification of a person or an organisation.", "additionalProperties": false, "properties": {"name": {"description": "Name by which a party is known and which is usually used to identify that party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "postal_address": {"description": "Information that locates and identifies a specific address, as defined by postal services.", "$ref": "#/definitions/PostalAddress24__2"}, "identification": {"description": "Unique and unambiguous identification of a party.", "$ref": "#/definitions/Party38Choice__3"}, "country_of_residence": {"description": "Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.", "$ref": "#/definitions/CountryCode"}}}, "PaymentIdentification7__1": {"type": "object", "description": "Provides further means of referencing a payment transaction.", "additionalProperties": false, "properties": {"instruction_identification": {"description": "Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the instruction.  Usage: The instruction identification is a point to point reference that can be used between the instructing party and the instructed party to refer to the individual instruction. It can be included in several messages related to the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text"}, "end_to_end_identification": {"description": "Unique identification, as assigned by the initiating party, to unambiguously identify the transaction. This identification is passed on, unchanged, throughout the entire end-to-end chain.  Usage: The end-to-end identification can be used for reconciliation or to link tasks relating to the transaction. It can be included in several messages related to the transaction.  Usage: In case there are technical limitations to pass on multiple references, the end-to-end identification must be passed on throughout the entire end-to-end chain.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "transaction_identification": {"description": "Unique identification, as assigned by the first instructing agent, to unambiguously identify the transaction that is passed on, unchanged, throughout the entire interbank chain.  Usage: The transaction identification can be used for reconciliation, tracking or to link tasks relating to the transaction on the interbank level.  Usage: The instructing agent has to make sure that the transaction identification is unique for a pre-agreed period.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "uetr": {"description": "Universally unique identifier to provide an end-to-end reference of a payment transaction.", "$ref": "#/definitions/UUIDv4Identifier"}, "clearing_system_reference": {"description": "Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["instruction_identification", "end_to_end_identification", "uetr"]}, "PaymentTypeInformation28__1": {"type": "object", "description": "Provides further details of the type of payment.", "additionalProperties": false, "properties": {"instruction_priority": {"description": "Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.", "$ref": "#/definitions/Priority2Code"}, "clearing_channel": {"description": "Specifies the clearing channel to be used to process the payment instruction.", "$ref": "#/definitions/ClearingChannel2Code"}, "service_level": {"type": "array", "maxItems": 3, "description": "Agreement under which or rules under which the transaction should be processed.", "items": {"$ref": "#/definitions/ServiceLevel8Choice__1"}}, "local_instrument": {"description": "User community specific instrument.  Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.", "$ref": "#/definitions/LocalInstrument2Choice__1"}, "category_purpose": {"description": "Specifies the high level purpose of the instruction based on a set of pre-defined categories. Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.", "$ref": "#/definitions/CategoryPurpose1Choice__1"}}}, "PercentageRate": {"type": "string", "description": "Rate expressed as a percentage, that is, in hundredths, for example, 0.7 is 7/10 of a percent, and 7.0 is 7%.", "maxLength": 12}, "PersonIdentification13__1": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__1"}}}}, "PersonIdentification13__2": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__2"}}}}, "PersonIdentification13__3": {"type": "object", "description": "Unique and unambiguous way to identify a person.", "additionalProperties": false, "properties": {"date_and_place_of_birth": {"description": "Date and place of birth of a person.", "$ref": "#/definitions/DateAndPlaceOfBirth1__1"}, "other": {"type": "array", "maxItems": 2, "description": "Unique identification of a person, as assigned by an institution, using an identification scheme.", "items": {"$ref": "#/definitions/GenericPersonIdentification1__3"}}}}, "PersonIdentificationSchemeName1Choice__1": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "PersonIdentificationSchemeName1Choice__2": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}]}, "PersonIdentificationSchemeName1Choice__3": {"type": "object", "description": "Sets of elements to identify a name of the identification scheme.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalPersonIdentification1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "PostalAddress24__1": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 3, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}}}}, "PostalAddress24__2": {"type": "object", "description": "Information that locates and identifies a specific address, as defined by postal services.", "additionalProperties": false, "properties": {"department": {"description": "Identification of a division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "sub_department": {"description": "Identification of a sub-division of a large organisation or building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "street_name": {"description": "Name of a street or thoroughfare.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "building_number": {"description": "Number that identifies the position of a building on a street.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "building_name": {"description": "Name of the building or house.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "floor": {"description": "Floor or storey within a building.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_box": {"description": "Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "room": {"description": "Building room number.", "$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}, "post_code": {"description": "Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.", "$ref": "#/definitions/CBPR_RestrictedFINXMax16Text_Extended"}, "town_name": {"description": "Name of a built-up area, with defined boundaries, and a local government.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "town_location_name": {"description": "Specific location name within the town.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "district_name": {"description": "Identifies a subdivision within a country sub-division.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country_sub_division": {"description": "Identifies a subdivision of a country such as state, region, county.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "country": {"description": "Nation with its own government.", "$ref": "#/definitions/CountryCode"}, "address_line": {"type": "array", "maxItems": 2, "description": "Information that locates and identifies a specific address, as defined by postal services, presented in free format text.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax70Text_Extended"}}}, "required": ["town_name", "country"]}, "Priority2Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["HIGH", "NORM"]}, "Priority3Code": {"type": "string", "description": "Specifies the priority level of an event.\n*`URGT`-Priority level is urgent (highest priority possible).\n*`HIGH`-Priority level is high.\n*`NORM`-Priority level is normal.", "enum": ["URGT", "HIGH", "NORM"]}, "ProxyAccountIdentification1__1": {"type": "object", "description": "Information related to a proxy  identification of the account.", "additionalProperties": false, "properties": {"type": {"description": "Type of the proxy identification.", "$ref": "#/definitions/ProxyAccountType1Choice__1"}, "identification": {"description": "Identification used to indicate the account identification under another specified name.", "$ref": "#/definitions/CBPR_RestrictedFINXMax320Text_Extended"}}, "required": ["identification"]}, "ProxyAccountType1Choice__1": {"type": "object", "description": "Specifies the scheme used for the identification of an account alias.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Name of the identification scheme, in a coded form as published in an external list.", "$ref": "#/definitions/ExternalProxyAccountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Name of the identification scheme, in a free text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "Purpose2Choice__1": {"type": "object", "description": "Specifies the underlying reason for the payment transaction.\nUsage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Underlying reason for the payment transaction, as published in an external purpose code list.", "$ref": "#/definitions/ExternalPurpose1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Purpose, in a proprietary form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "ReferredDocumentInformation7__1": {"type": "object", "description": "Set of elements used to identify the documents referred to in the remittance information.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of referred document.", "$ref": "#/definitions/ReferredDocumentType4__1"}, "number": {"description": "Unique and unambiguous identification of the referred document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "related_date": {"description": "Date associated with the referred document.", "$ref": "#/definitions/ISODate"}, "line_details": {"type": "array", "description": "Set of elements used to provide the content of the referred document line.", "items": {"$ref": "#/definitions/DocumentLineInformation1__1"}}}}, "ReferredDocumentType3Choice__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Document type in a coded form.", "$ref": "#/definitions/DocumentType6Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Proprietary identification of the type of the remittance document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "ReferredDocumentType4__1": {"type": "object", "description": "Specifies the type of the document referred in the remittance information.", "additionalProperties": false, "properties": {"code_or_proprietary": {"description": "Provides the type details of the referred document.", "$ref": "#/definitions/ReferredDocumentType3Choice__1"}, "issuer": {"description": "Identification of the issuer of the reference document type.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["code_or_proprietary"]}, "RegulatoryAuthority2__1": {"type": "object", "description": "Entity requiring the regulatory reporting information.", "additionalProperties": false, "properties": {"name": {"description": "Name of the entity requiring the regulatory reporting information.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text"}, "country": {"description": "Country of the entity that requires the regulatory reporting information.", "$ref": "#/definitions/CountryCode"}}}, "RegulatoryReporting3__1": {"type": "object", "description": "Information needed due to regulatory and/or statutory requirements.", "additionalProperties": false, "properties": {"debit_credit_reporting_indicator": {"description": "Identifies whether the regulatory reporting information applies to the debit side, to the credit side or to both debit and credit sides of the transaction.", "$ref": "#/definitions/RegulatoryReportingType1Code"}, "authority": {"description": "Entity requiring the regulatory reporting information.", "$ref": "#/definitions/RegulatoryAuthority2__1"}, "details": {"type": "array", "description": "Set of elements used to provide details on the regulatory reporting information.", "items": {"$ref": "#/definitions/StructuredRegulatoryReporting3__1"}}}}, "RegulatoryReportingType1Code": {"type": "string", "description": "Identifies whether the regulatory reporting information applies to the debit side, to the credit side or to both debit and credit sides of the transaction.\n*`CRED`-Regulatory information applies to the credit side.\n*`DEBT`-Regulatory information applies to the debit side.\n*`BOTH`-Regulatory information applies to both credit and debit sides.", "enum": ["CRED", "DEBT", "BOTH"]}, "RemittanceAmount2__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"due_payable_amount": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "discount_applied_amount": {"type": "array", "description": "Amount specified for the referred document is the amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}}, "credit_note_amount": {"description": "Amount specified for the referred document is the amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "tax_amount": {"type": "array", "description": "Quantity of cash resulting from the calculation of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}}, "adjustment_amount_and_reason": {"type": "array", "description": "Specifies detailed information on the amount and reason of the document adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}}, "remitted_amount": {"description": "Amount of money remitted for the referred document.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}}, "RemittanceAmount3__1": {"type": "object", "description": "Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.", "additionalProperties": false, "properties": {"due_payable_amount": {"description": "Amount specified is the exact amount due and payable to the creditor.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "discount_applied_amount": {"type": "array", "description": "Amount of discount to be applied to the amount due and payable to the creditor.", "items": {"$ref": "#/definitions/DiscountAmountAndType1__1"}}, "credit_note_amount": {"description": "Amount of a credit note.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "tax_amount": {"type": "array", "description": "Amount of the tax.", "items": {"$ref": "#/definitions/TaxAmountAndType1__1"}}, "adjustment_amount_and_reason": {"type": "array", "description": "Specifies detailed information on the amount and reason of the adjustment.", "items": {"$ref": "#/definitions/DocumentAdjustment1__1"}}, "remitted_amount": {"description": "Amount of money remitted.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}}, "RemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system.", "additionalProperties": false, "properties": {"unstructured": {"description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in an unstructured form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "structured": {"type": "array", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "items": {"$ref": "#/definitions/StructuredRemittanceInformation16__1"}}}}, "RemittanceLocation7__1": {"type": "object", "description": "Provides information on the remittance advice.", "additionalProperties": false, "properties": {"remittance_identification": {"description": "Unique identification, as assigned by the initiating party, to unambiguously identify the remittance information sent separately from the payment instruction, such as a remittance advice.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "remittance_location_details": {"type": "array", "maxItems": 2, "description": "Set of elements used to provide information on the location and/or delivery of the remittance information.", "items": {"$ref": "#/definitions/RemittanceLocationData1__1"}}}}, "RemittanceLocationData1__1": {"type": "object", "description": "Provides additional details on the remittance advice.", "additionalProperties": false, "properties": {"method": {"description": "Method used to deliver the remittance advice information.", "$ref": "#/definitions/RemittanceLocationMethod2Code"}, "electronic_address": {"description": "Electronic address to which an agent is to send the remittance information.", "$ref": "#/definitions/CBPR_RestrictedFINXMax2048Text_Extended"}, "postal_address": {"description": "Postal address to which an agent is to send the remittance information.", "$ref": "#/definitions/NameAndAddress16__1"}}, "required": ["method"]}, "RemittanceLocationMethod2Code": {"type": "string", "description": "Specifies the method used to deliver the remittance advice information.\n*`FAXI`-Remittance advice information must be faxed.\n*`EDIC`-Remittance advice information must be sent through Electronic Data Interchange (EDI).\n*`URID`-Remittance advice information needs to be sent to a Uniform Resource Identifier (URI). URI is a compact string of characters that uniquely identify an abstract or physical resource. URI's are the super-set of identifiers, such as URLs, email addresses, ftp sites, etc, and as such, provide the syntax for all of the identification schemes.\n*`EMAL`-Remittance advice information must be sent through e-mail.\n*`POST`-Remittance advice information must be sent through postal services.\n*`SMSM`-Remittance advice information must be sent through by phone as a short message service (SMS).", "enum": ["FAXI", "EDIC", "URID", "EMAL", "POST", "SMSM"]}, "ServiceLevel8Choice__1": {"type": "object", "description": "Specifies the service level of the transaction.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.", "$ref": "#/definitions/ExternalServiceLevel1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies a pre-agreed service or level of service between the parties, as a proprietary code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}, "required": ["proprietary"]}]}, "SettlementDateTimeIndication1__1": {"type": "object", "description": "Information on the occurred settlement time(s) of the payment transaction.", "additionalProperties": false, "properties": {"debit_date_time": {"description": "Date and time at which a payment has been debited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been debited at the central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime"}, "credit_date_time": {"description": "Date and time at which a payment has been credited at the transaction administrator. In the case of TARGET, the date and time at which the payment has been credited at the receiving central bank, expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_DateTime"}}}, "SettlementInstruction7__1": {"type": "object", "description": "Provides further details on the settlement of the instruction.", "additionalProperties": false, "properties": {"settlement_method": {"description": "Method used to settle the (batch of) payment instructions.", "$ref": "#/definitions/SettlementMethod1Code__1"}, "settlement_account": {"description": "A specific purpose account used to post debit and credit entries as a result of the transaction.", "$ref": "#/definitions/CashAccount38__1"}, "instructing_reimbursement_agent": {"description": "Agent through which the instructing agent will reimburse the instructed agent.  Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "instructing_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the instructing reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "instructed_reimbursement_agent": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If InstructedReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch. Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "instructed_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the instructed reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}, "third_reimbursement_agent": {"description": "Agent at which the instructed agent will be reimbursed. Usage: If ThirdReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.", "$ref": "#/definitions/BranchAndFinancialInstitutionIdentification6__1"}, "third_reimbursement_agent_account": {"description": "Unambiguous identification of the account of the third reimbursement agent account at its servicing agent in the payment chain.", "$ref": "#/definitions/CashAccount38__1"}}, "required": ["settlement_method"]}, "SettlementMethod1Code__1": {"type": "string", "description": "Specifies the method used to settle the credit transfer instruction.\n*`INDA`-Settlement is done by the agent instructed to execute a payment instruction.\n*`INGA`-Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.\n*`COVE`-Settlement is done through a cover payment.", "enum": ["INDA", "INGA", "COVE"]}, "SettlementTimeRequest2__1": {"type": "object", "description": "Provides information on the requested settlement time(s) of the payment instruction.", "additionalProperties": false, "properties": {"cls_time": {"description": "Time by which the amount of money must be credited, with confirmation, to the CLS Bank's account at the central bank. Usage: Time must be expressed in Central European Time (CET).", "$ref": "#/definitions/CBPR_Time"}, "till_time": {"description": "Time until when the payment may be settled.", "$ref": "#/definitions/CBPR_Time"}, "from_time": {"description": "Time as from when the payment may be settled.", "$ref": "#/definitions/CBPR_Time"}, "reject_time": {"description": "Time by when the payment must be settled to avoid rejection.", "$ref": "#/definitions/CBPR_Time"}}}, "StructuredRegulatoryReporting3__1": {"type": "object", "description": "Information needed due to regulatory and statutory requirements.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the information supplied in the regulatory reporting details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}, "date": {"description": "Date related to the specified type of regulatory reporting details.", "$ref": "#/definitions/ISODate"}, "country": {"description": "Country related to the specified type of regulatory reporting details.", "$ref": "#/definitions/CountryCode"}, "code": {"description": "Specifies the nature, purpose, and reason for the transaction to be reported for regulatory and statutory requirements in a coded form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax10Text"}, "amount": {"description": "Amount of money to be reported for regulatory and statutory requirements.", "$ref": "#/definitions/CBPR_Amount__1"}, "information": {"type": "array", "description": "Additional details that cater for specific domestic regulatory requirements.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax35Text"}}}}, "StructuredRemittanceInformation16__1": {"type": "object", "description": "Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.", "additionalProperties": false, "properties": {"referred_document_information": {"type": "array", "description": "Provides the identification and the content of the referred document.", "items": {"$ref": "#/definitions/ReferredDocumentInformation7__1"}}, "referred_document_amount": {"description": "Provides details on the amounts of the referred document.", "$ref": "#/definitions/RemittanceAmount2__1"}, "creditor_reference_information": {"description": "Reference information provided by the creditor to allow the identification of the underlying documents.", "$ref": "#/definitions/CreditorReferenceInformation2__1"}, "invoicer": {"description": "Identification of the organisation issuing the invoice, when it is different from the creditor or ultimate creditor.", "$ref": "#/definitions/PartyIdentification135__4"}, "invoicee": {"description": "Identification of the party to whom an invoice is issued, when it is different from the debtor or ultimate debtor.", "$ref": "#/definitions/PartyIdentification135__4"}, "tax_remittance": {"description": "Provides remittance information about a payment made for tax-related purposes.", "$ref": "#/definitions/TaxInformation7__1"}, "garnishment_remittance": {"description": "Provides remittance information about a payment for garnishment-related purposes.", "$ref": "#/definitions/Garnishment3__1"}, "additional_remittance_information": {"type": "array", "maxItems": 3, "description": "Additional information, in free text form, to complement the structured remittance information.", "items": {"$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}}, "TaxAmount2": {"type": "object", "description": "Set of elements used to provide information on the tax amount(s) of tax record.", "additionalProperties": false, "properties": {"rate": {"description": "Rate used to calculate the tax.", "$ref": "#/definitions/PercentageRate"}, "taxable_base_amount": {"description": "Amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "total_amount": {"description": "Total amount that is the result of the calculation of the tax for the record.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "details": {"type": "array", "description": "Set of elements used to provide details on the tax period and amount.", "items": {"$ref": "#/definitions/TaxRecordDetails2"}}}}, "TaxAmountAndType1__1": {"type": "object", "description": "Specifies the amount with a specific type.", "additionalProperties": false, "properties": {"type": {"description": "Specifies the type of the amount.", "$ref": "#/definitions/TaxAmountType1Choice__1"}, "amount": {"description": "Amount of money, which has been typed.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "TaxAmountType1Choice__1": {"type": "object", "description": "Specifies the amount type.", "additionalProperties": true, "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"code": {"description": "Specifies the amount type, in a coded form.", "$ref": "#/definitions/ExternalTaxAmountType1Code"}}, "required": ["code"]}, {"type": "object", "additionalProperties": false, "properties": {"proprietary": {"description": "Specifies the amount type, in a free-text form.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}, "required": ["proprietary"]}]}, "TaxAuthorisation1__1": {"type": "object", "description": "Details of the authorised tax paying party.", "additionalProperties": false, "properties": {"title": {"description": "Title or position of debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "name": {"description": "Name of the debtor or the debtor's authorised representative.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}, "TaxInformation7__1": {"type": "object", "description": "Details about tax paid, or to be paid, to the government in accordance with the law, including pre-defined parameters such as thresholds and type of account.", "additionalProperties": false, "properties": {"creditor": {"description": "Party on the credit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty1__1"}, "debtor": {"description": "Identifies the party on the debit side of the transaction to which the tax applies.", "$ref": "#/definitions/TaxParty2__1"}, "ultimate_debtor": {"description": "Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the taxing authority.", "$ref": "#/definitions/TaxParty2__1"}, "administration_zone": {"description": "Territorial part of a country to which the tax payment is related.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "reference_number": {"description": "Tax reference information that is specific to a taxing agency.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}, "method": {"description": "Method used to indicate the underlying business or how the tax is paid.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "total_taxable_base_amount": {"description": "Total amount of money on which the tax is based.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "total_tax_amount": {"description": "Total amount of money as result of the calculation of the tax.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}, "date": {"description": "Date by which tax is due.", "$ref": "#/definitions/ISODate"}, "sequence_number": {"description": "Sequential number of the tax report.", "$ref": "#/definitions/Number"}, "record": {"type": "array", "description": "Record of tax details.", "items": {"$ref": "#/definitions/TaxRecord2__1"}}}}, "TaxParty1__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"tax_identification": {"description": "Tax identification number of the creditor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "registration_identification": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "tax_type": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}}}, "TaxParty2__1": {"type": "object", "description": "Details about the entity involved in the tax paid or to be paid.", "additionalProperties": false, "properties": {"tax_identification": {"description": "Tax identification number of the debtor.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "registration_identification": {"description": "Unique identification, as assigned by an organisation, to unambiguously identify a party.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "tax_type": {"description": "Type of tax payer.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "authorisation": {"description": "Details of the authorised tax paying party.", "$ref": "#/definitions/TaxAuthorisation1__1"}}}, "TaxPeriod2": {"type": "object", "description": "Period of time details related to the tax payment.", "additionalProperties": false, "properties": {"year": {"description": "Year related to the tax payment.", "$ref": "#/definitions/ISODate"}, "type": {"description": "Identification of the period related to the tax payment.", "$ref": "#/definitions/TaxRecordPeriod1Code"}, "from_to_date": {"description": "Range of time between a start date and an end date for which the tax report is provided.", "$ref": "#/definitions/DatePeriod2"}}}, "TaxRecord2__1": {"type": "object", "description": "Set of elements used to define the tax record.", "additionalProperties": false, "properties": {"type": {"description": "High level code to identify the type of tax details.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "category": {"description": "Specifies the tax code as published by the tax authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "category_details": {"description": "Provides further details of the category tax code.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "debtor_status": {"description": "Code provided by local authority to identify the status of the party that has drawn up the settlement document.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "certificate_identification": {"description": "Identification number of the tax report as assigned by the taxing authority.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "forms_code": {"description": "Identifies, in a coded form, on which template the tax report is to be provided.", "$ref": "#/definitions/CBPR_RestrictedFINXMax35Text_Extended"}, "period": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2"}, "tax_amount": {"description": "Set of elements used to provide information on the amount of the tax record.", "$ref": "#/definitions/TaxAmount2"}, "additional_information": {"description": "Further details of the tax record.", "$ref": "#/definitions/CBPR_RestrictedFINXMax140Text_Extended"}}}, "TaxRecordDetails2": {"type": "object", "description": "Provides information on the individual tax amount(s) per period of the tax record.", "additionalProperties": false, "properties": {"period": {"description": "Set of elements used to provide details on the period of time related to the tax payment.", "$ref": "#/definitions/TaxPeriod2"}, "amount": {"description": "Underlying tax amount related to the specified period.", "$ref": "#/definitions/ActiveOrHistoricCurrencyAndAmount"}}, "required": ["amount"]}, "TaxRecordPeriod1Code": {"type": "string", "description": "Specifies the period related to the tax payment.\n*`MM01`-Tax is related to the second month of the period.\n*`MM02`-Tax is related to the first month of the period.\n*`MM03`-Tax is related to the third month of the period.\n*`MM04`-Tax is related to the fourth month of the period.\n*`MM05`-Tax is related to the fifth month of the period.\n*`MM06`-Tax is related to the sixth month of the period.\n*`MM07`-Tax is related to the seventh month of the period.\n*`MM08`-Tax is related to the eighth month of the period.\n*`MM09`-Tax is related to the ninth month of the period.\n*`MM10`-Tax is related to the tenth month of the period.\n*`MM11`-Tax is related to the eleventh month of the period.\n*`MM12`-Tax is related to the twelfth month of the period.\n*`QTR1`-Tax is related to the first quarter of the period.\n*`QTR2`-Tax is related to the second quarter of the period.\n*`QTR3`-Tax is related to the third quarter of the period.\n*`QTR4`-Tax is related to the forth quarter of the period.\n*`HLF1`-Tax is related to the first half of the period.\n*`HLF2`-Tax is related to the second half of the period.", "enum": ["MM01", "MM02", "MM03", "MM04", "MM05", "MM06", "MM07", "MM08", "MM09", "MM10", "MM11", "MM12", "QTR1", "QTR2", "QTR3", "QTR4", "HLF1", "HLF2"]}, "TrueFalseIndicator": {"type": "boolean", "description": "A flag indicating a True or False value."}, "UUIDv4Identifier": {"type": "string", "description": "Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 \"Universally Unique IDentifier (UUID) URN Namespace\".", "pattern": "^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$"}}}