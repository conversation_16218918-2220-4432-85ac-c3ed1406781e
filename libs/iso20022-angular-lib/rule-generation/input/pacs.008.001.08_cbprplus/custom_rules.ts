// TODO: Go through fields and figure out which are serverOnly.

import {
  CustomRule,
  RequiredDefinition,
} from '../../../projects/iso20022-lib/rules';

// Rules not encoded:
// R15 - "FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "Whenever Debtor Agent, Creditor Agent and all agents in between are located within the same country, the clearing code (ClrSysMmbId I guess?) only may be used." -> BICFI only required if not in same country. We can add an info popup for this but cannot enforce it as a rule.
// R19 - "FIToFICstmrCdtTrf-GrpHdr-SttlmInf" + "-InstgRmbrsmntAgt"/"-InstdRmbrsmntAgt"/"-ThrdRmbrsmntAgt": "If the transaction is exchanged on the SWIFT network (i.e. if the sender and receiver of the message are on SWIFT), then BIC is mandatory and other elements are optional, e.g. LEI"

// Define what it means to require certain definitions.
// The script parsing the rules will use these to expand the rules. E.g. it is sufficient to specifiy "PstlAddr" in a "require" rule and the script will figure out which definition that corresponds to and expand the rule to include all required subfields of that definition.
// IMPORTANT: This does not denote exclusive ors. An "or" here means that the rule is fulfilled if at least one of the constituents is present.
// IMPORTANT: This denotes the minimal fields that are required to be present. There might be additional rules that add more nuance. E.g. there are numerous rules that describe the relationship of the fields in 'BranchAndFinancialInstitutionIdentification6__1' (see R16, R17, R18, R20, R25, R29), but to express that the 'InstgRmbrsmntAgt' is required, it is sufficient to say, that either the 'BICFI' or the 'Nm' is required. The other rules will then ensure that the other fields are present if one of them is present.
export const sharedRequiredDefinitions: Record<string, RequiredDefinition> = {
  PostalAddress24__1: {
    or: ['AdrLine', ['TwnNm', 'Ctry']], // R21, R26, R30
  },
  BranchAndFinancialInstitutionIdentification6__1: {
    or: ['FinInstnId-BICFI', 'FinInstnId-Nm'],
  },
};

export const customRules: CustomRule[] = [
  {
    id: 'FIToFICstmrCdtTrf-GrpHdr-MsgId-serverOnly',
    description: 'MessageIdentification is serverOnly',
    type: 'serverOnly',
    value: true,
    target: 'FIToFICstmrCdtTrf-GrpHdr-MsgId',
  },
  {
    id: 'FIToFICstmrCdtTrf-GrpHdr-NbOfTxs-serverOnly',
    description: 'NumberOfTransactions is serverOnly',
    type: 'serverOnly',
    value: true,
    target: 'FIToFICstmrCdtTrf-GrpHdr-NbOfTxs',
  },
  {
    id: 'FIToFICstmrCdtTrf-GrpHdr-NbOfTxs-value',
    description: 'NumberOfTransactions must be exactly 1',
    type: 'value',
    value: '1',
    isEqual: true,
    target: 'FIToFICstmrCdtTrf-GrpHdr-NbOfTxs',
  },
  {
    id: 'R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-conditional-required',
    description:
      "If at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' is present, then at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr' must be present.",
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm',
      },
    ],
    rules: [
      {
        id: 'R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-required-conditional',
        description:
          'At least one occurrence of the element PostalAddress must be present.',
        type: 'required',
        value: true,
        target: 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr',
      },
    ],
  },
  {
    id: 'R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm-conditional-required',
    description:
      "If at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr' is present, then at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' must be present.",
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr',
      },
    ],
    rules: [
      {
        id: 'R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm-required-conditional',
        description:
          "At least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' must be present.",
        type: 'required',
        value: true,
        target: 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm',
      },
    ],
  },
  {
    id: 'R13:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-conditional-value',
    description:
      "The code 'HOLD' is not allowed if the code 'CHQB' is present.",
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'CHQB',
        field: 'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd', // TODO: Cd is an array and the directive must be able to handle this
      },
    ],
    rules: [
      {
        id: 'R13:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-value-conditional',
        description: "The code 'HOLD' is not allowed.",
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'HOLD',
        isEqual: false,
        target: 'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd',
      },
    ],
  },
  {
    id: 'R14:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-conditional-value',
    description:
      "The code 'TELB' is not allowed if the code 'PHOB' is present.",
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'PHOB',
        field: 'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd', // TODO: Cd is an array and the directive must be able to handle this
      },
    ],
    rules: [
      {
        id: 'R14:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-value-conditional',
        description: "The code 'TELB' is not allowed.",
        type: 'value', // Not using a pattern here because that would override other patterns for this field which are still valid.
        value: 'TELB',
        isEqual: false,
        target: 'FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd',
      },
    ],
  },
  {
    id: 'R16:FinInstnId-ClrSysMmbId_Nm_PstlAddr-conditional-prohibited',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If the BICFI is present, it must not be accompanied by a Clearing Code, a Name, or an Address',
    type: 'condition',
    conditions: [
      {
        field: 'FinInstnId-BICFI',
        type: 'present',
        value: true,
      },
    ],
    rules: [
      {
        id: 'R16:FinInstnId-ClrSysMmbId-prohibited-conditional',
        description: 'The BICFI must not be accompanied by a Clearing Code.',
        type: 'prohibited',
        value: true,
        target: 'FinInstnId-ClrSysMmbId',
      },
      {
        id: 'R16:FinInstnId-Nm-prohibited-conditional',
        description: 'The BICFI must not be accompanied by a Name.',
        type: 'prohibited',
        value: true,
        target: 'FinInstnId-Nm',
      },
      {
        id: 'R16:FinInstnId-PstlAdr-prohibited-conditional',
        description: 'The BICFI must not be accompanied by a Postal Address.',
        type: 'prohibited',
        value: true,
        target: 'FinInstnId-PstlAdr',
      },
    ],
  },
  {
    id: 'R17-R18:FinInstnId-BICFI-conditional-prohibited',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If the Clearing Code or the Name or the Address is present, the BICFI must not be present.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-ClrSysMmbId',
      },
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-Nm',
      },
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-PstlAdr',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: 'R17-R18:FinInstnId-BICFI-prohibited-conditional',
        description: 'The BICFI must not be present.',
        type: 'prohibited',
        value: true,
        target: 'FinInstnId-BICFI',
      },
    ],
  },
  {
    id: 'R17-R18:FinInstnId-PstlAdr-conditional-required', // This is also covered by R20.
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If the Name is present, either the unstructed postal address or town name and country are required.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-Nm',
      },
    ],
    rules: [
      {
        id: 'R17-R18:FinInstnId-PstlAdr-required-conditional',
        description: 'The Postal Address is required.',
        type: 'required',
        value: true,
        target: 'FinInstnId-PstlAdr',
      },
    ],
  },
  {
    id: 'R17-R18:FinInstnId-Nm-conditional-required', // This is also covered by R20-R25.
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description: 'If the Postal Address is present, the name is required.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-PstlAdr',
      },
    ],
    rules: [
      {
        id: 'R17-R18:FinInstnId-Nm-required-conditional',
        description: 'The Name is required.',
        type: 'required',
        value: true,
        target: 'FinInstnId-Nm',
      },
    ],
  },
  {
    id: 'R20-R25-R29:FinInstnId-PstlAdr-conditional-required',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If at least one occurrence of the element Name is present, then at least one occurrence of the element Postal Address must be present.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-Nm',
      },
    ],
    rules: [
      {
        id: 'R20-R25-R29:FinInstnId-PstlAdr-required-conditional',
        description:
          'At least one occurrence of the element Postal Address must be present.',
        type: 'required',
        value: true,
        target: 'FinInstnId-PstlAdr',
      },
    ],
  },
  {
    id: 'R20-R25-R29:FinInstnId-Nm-conditional-required',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If at least one occurrence of the element Postal Address is present, then at least one occurrence of the element Name must be present.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-PstlAdr',
      },
    ],
    rules: [
      {
        id: 'R20-R25-R29:FinInstnId-Nm-required-conditional',
        description:
          'At least one occurrence of the element Name must be present.',
        type: 'required',
        value: true,
        target: 'FinInstnId-Nm',
      },
    ],
  },
  {
    id: 'R22-R27-R31:FinInstnId-PstlAdr-TwnNm_Ctry-conditional-required',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If the Address Line is present, and either Department or SubDepartment or StreetName or BuildingNumber or BuildingName or Floor or PostBox or Room or PostCode or TownLocationName or DistrictName or CountrySubDivision are present, then TownName and Country are mandatory and a maximum of two occurrences of Address Line are allowed.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'FinInstnId-PstlAdr-AdrLine',
      },
      {
        conditions: [
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-Dept',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-SubDept',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-StrtNm',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-BldgNb',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-BldgNm',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-Flr',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-PstBx',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-Room',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-PstCd',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-TwnLctnNm',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-DstrctNm',
          },
          {
            type: 'present',
            value: true,
            field: 'FinInstnId-PstlAdr-CtrySubDvsn',
          },
        ],
        conditionsConnector: 'or',
      },
    ],
    conditionsConnector: 'and',
    rules: [
      {
        id: 'R22-R27-R31:FinInstnId-PstlAdr-TwnNm-required-conditional',
        description:
          'TownName is mandatory if the Address Line is present and any of the other fields are present.',
        type: 'required',
        value: true,
        target: 'FinInstnId-PstlAdr-TwnNm',
      },
      {
        id: 'R22-R27-R31:FinInstnId-PstlAdr-Ctry-required-conditional',
        description:
          'Country is mandatory if the Address Line is present and any of the other fields are present.',
        type: 'required',
        value: true,
        target: 'FinInstnId-PstlAdr-Ctry',
      },
      {
        id: 'R22-R27-R31:FinInstnId-PstlAdr-AdrLine-maxItems-conditional',
        description:
          'A maximum of two occurrences of Address Line are allowed.',
        type: 'maxItems',
        value: 2,
        target: 'FinInstnId-PstlAdr-AdrLine',
      },
    ],
  },
  {
    id: 'R23:FinInstnId-PstlAdr-AdrLine-conditional-contains',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'Data present in structured elements within the Postal Address must not be repeated in AddressLine.',
    type: 'contains',
    contains: false,
    target: 'FinInstnId-PstlAdr-AdrLine',
    otherFields: [
      'FinInstnId-PstlAdr-Dept',
      'FinInstnId-PstlAdr-SubDept',
      'FinInstnId-PstlAdr-StrtNm',
      'FinInstnId-PstlAdr-BldgNb',
      'FinInstnId-PstlAdr-BldgNm',
      'FinInstnId-PstlAdr-Flr',
      'FinInstnId-PstlAdr-PstBx',
      'FinInstnId-PstlAdr-Room',
      'FinInstnId-PstlAdr-PstCd',
      'FinInstnId-PstlAdr-TwnNm',
      'FinInstnId-PstlAdr-TwnLctnNm',
      'FinInstnId-PstlAdr-DstrctNm',
      'FinInstnId-PstlAdr-CtrySubDvsn',
      'FinInstnId-PstlAdr-Ctry',
    ],
  },
  {
    id: 'R24-R28-R32:FinInstnId-PstlAdr-AdrLine-conditional-maxLength',
    baseKeys: [
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt',
      'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt',
    ],
    description:
      'If Postal Address is present and if no other element than Address Line is present then every occurrence of Address Line must not exceed 35 characters.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-Dept',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-SubDept',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-StrtNm',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-BldgNb',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-BldgNm',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-Flr',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-PstBx',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-Room',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-PstCd',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-TwnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-TwnLctnNm',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-DstrctNm',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-CtrySubDvsn',
      },
      {
        type: 'present',
        value: false,
        field: 'FinInstnId-PstlAdr-Ctry',
      },
    ],
    rules: [
      {
        id: 'R24-R28-R32:FinInstnId-PstlAdr-AdrLine-maxLength-conditional',
        description: 'AddressLine must be at most 35 characters long.',
        type: 'maxLength',
        value: 35,
        target: 'FinInstnId-PstlAdr-AdrLine',
      },
    ],
  },
  {
    id: 'ThirdReimbursementAgentRule:InstgRmbrsmntAgt-conditional-required',
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    description:
      'If ThirdReimbursementAgent is present, then InstructingReimbursementAgent must be present.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: 'ThirdReimbursementAgentRule:InstgRmbrsmntAgt-required-conditional',
        description:
          'InstructingReimbursementAgent is required if ThirdReimbursementAgent is present.',
        type: 'required',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
    ],
  },
  {
    id: 'ThirdReimbursementAgentRule:InstdRmbrsmntAgt-conditional-required',
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    description:
      'If ThirdReimbursementAgent is present, then InstructedReimbursementAgent must be present.',
    type: 'condition',
    conditions: [
      {
        type: 'present',
        value: true,
        field: 'ThrdRmbrsmntAgt',
      },
    ],
    rules: [
      {
        id: 'ThirdReimbursementAgentRule:InstdRmbrsmntAgt-required-conditional',
        description:
          'InstructedReimbursementAgent is required if ThirdReimbursementAgent is present.',
        type: 'required',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
    ],
  },
  {
    id: 'SettlementMethodAgentRule:InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt-conditional-prohibited',
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    description:
      'If SettlementMethod is equal to INDA or INGA, then ReimbursementAgent(s) and ClearingSystem are not allowed.', // Note: "/Document/FIToFICstmrCdtTrf/GrpHdr/SttlmInf/ClrSys" was removed from the guidelines and is thus not included in this rule.
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'INDA',
        field: 'SttlmMtd',
      },
      {
        type: 'value',
        value: 'INGA',
        field: 'SttlmMtd',
      },
    ],
    conditionsConnector: 'or',
    rules: [
      {
        id: 'SettlementMethodAgentRule:InstgRmbrsmntAgt-prohibited-conditional',
        description:
          'InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.',
        type: 'prohibited',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
      {
        id: 'SettlementMethodAgentRule:InstdRmbrsmntAgt-prohibited-conditional',
        description:
          'InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.',
        type: 'prohibited',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: 'SettlementMethodAgentRule:ThrdRmbrsmntAgt-prohibited-conditional',
        description:
          'ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.',
        type: 'prohibited',
        value: true,
        target: 'ThrdRmbrsmntAgt',
      },
    ],
  },
  {
    id: 'SettlementMethodCoverRule:SttlmAcct-conditional-prohibited',
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    description:
      'If SettlementMethod is equal to COVE, then SettlementAccount and ClearingSystem are not allowed.',
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: 'SettlementMethodCoverRule:SttlmAcct-prohibited-conditional',
        description:
          'SettlementAccount is not allowed if SettlementMethod is COVE.',
        type: 'prohibited',
        value: true,
        target: 'SttlmAcct',
      },
    ],
  },
  {
    id: 'SettlementMethodCoverAgentRule:InstdRmbrsmntAgt_InstgRmbrsmntAgt-conditional-required',
    baseKeys: ['FIToFICstmrCdtTrf-GrpHdr-SttlmInf'],
    description:
      'If SettlementMethod is equal to COVE, then InstructedReimbursementAgent or InstructingReimbursementAgent must be present.',
    type: 'condition',
    conditions: [
      {
        type: 'value',
        value: 'COVE',
        field: 'SttlmMtd',
      },
    ],
    rules: [
      {
        id: 'SettlementMethodCoverAgentRule:InstdRmbrsmntAgt-required-conditional',
        description:
          'If SettlementMethod is equal to COVE, then InstructedReimbursementAgent or InstructingReimbursementAgent must be present.',
        type: 'required',
        value: true,
        target: 'InstdRmbrsmntAgt',
      },
      {
        id: 'SettlementMethodCoverAgentRule:InstgRmbrsmntAgt-required-conditional',
        description:
          'If SettlementMethod is equal to COVE, then InstructedReimbursementAgent or InstructingReimbursementAgent must be present.',
        type: 'required',
        value: true,
        target: 'InstgRmbrsmntAgt',
      },
    ],
    rulesConnector: 'or',
  },
  // R33, 34, 35, 36, 37
];
