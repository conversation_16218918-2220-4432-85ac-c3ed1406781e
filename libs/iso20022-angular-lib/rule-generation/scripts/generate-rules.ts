import {
  Condition,
  ConditionalRule,
  PatternRule,
  ProhibitedRule,
  RequiredRule,
  Rule,
} from '../../projects/iso20022-lib/rules';
import {
  addBaseKey,
  getName,
  getNestedFieldNames,
  getRef,
  isValidObject,
} from './utils';

function getRequiredRule(
  fieldName: string,
  nestedFieldName: string
): RequiredRule {
  return {
    id: `generated:${nestedFieldName}-required`,
    description: `${fieldName} is required`,
    type: 'required',
    value: true,
    target: nestedFieldName,
  };
}

function getPatternRule(
  fieldName: string,
  nestedFieldName: string,
  regex?: string,
  enumValues?: string[]
): PatternRule {
  // Escape special regex characters in the strings.
  const escapedEnumValues = enumValues?.map((value) =>
    value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  );
  const joinedEnumValues = escapedEnumValues
    ? `^(${escapedEnumValues.join('|')})?$`
    : undefined;
  const pattern = regex || joinedEnumValues;

  if (!pattern) {
    throw new Error(
      `Invalid JSON schema: '${fieldName}' has no valid pattern or enum values.`
    );
  }

  return {
    id: `generated:${nestedFieldName}-pattern`,
    description: regex
      ? `${fieldName} must match the pattern ${regex}.`
      : `${fieldName} must be one of the following values: ${enumValues?.join(
          ', '
        )}.`,
    type: 'pattern',
    value: pattern,
    target: nestedFieldName,
  };
}

function getConditionalRequiredRules(
  oneOf: unknown[],
  definitions: Record<string, unknown>,
  nestedFieldName: string
): ConditionalRule[] {
  const nestedFieldNamesPerBranch: Record<string, string[]> = {};
  const hypotheticalNestedRequiredRulesPerBranch: Record<
    string,
    RequiredRule[]
  > = {};

  for (const oneOfEntry of oneOf) {
    const data = getDataFromOneOfEntry(oneOfEntry);
    if (!data) {
      continue;
    }
    const { propertyKey, propertyRef, propertyName } = data;

    const nestedFieldNames = getNestedFieldNames(
      definitions,
      propertyRef,
      `${nestedFieldName}-${propertyKey}`
    );
    nestedFieldNamesPerBranch[propertyKey] = nestedFieldNames;

    const hypotheticalNestedRequiredRules: RequiredRule[] = getRules(
      definitions,
      propertyRef,
      propertyName ?? propertyKey,
      `${nestedFieldName}-${propertyKey}`,
      true,
      true
    ).filter((rule) => rule.type === 'required'); // Filter is only necessary so the "RequiredRule" type is correctly inferred.
    hypotheticalNestedRequiredRulesPerBranch[propertyKey] =
      hypotheticalNestedRequiredRules;
  }

  const conditionalRequiredRules: ConditionalRule[] = [];

  for (const [propertyKey, nestedRequiredRules] of Object.entries(
    hypotheticalNestedRequiredRulesPerBranch
  )) {
    // The condition for applying the required rules is that all fields in the other branches are not set.
    const fieldNamesOfOtherBranches: string[] = Object.entries(
      nestedFieldNamesPerBranch
    )
      .filter(([otherPropertyKey]) => otherPropertyKey !== propertyKey)
      .flatMap(([, nestedFieldNames]) => nestedFieldNames);
    const notPresentConditions: Condition[] = fieldNamesOfOtherBranches.map(
      (fieldName) => ({
        field: fieldName,
        type: 'present',
        value: false,
      })
    );

    const conditionalRequiredRulesForBranch: ConditionalRule[] =
      nestedRequiredRules.map((rule) => {
        const ruleIdBase = rule.id.replace(/-required$/, '');
        const conditionalRuleId = `generated:${ruleIdBase}-conditional-required`;
        const description = `${rule.description} if ${notPresentConditions
          .map((condition) => `${condition.field} is not present`)
          .join(' and ')}`;
        return {
          id: conditionalRuleId,
          description,
          type: 'condition',
          target: rule.target,
          conditions: notPresentConditions,
          rules: [
            {
              ...rule,
              id: `generated:${ruleIdBase}-required-conditional`,
              description,
            },
          ],
        };
      });

    conditionalRequiredRules.push(...conditionalRequiredRulesForBranch);
  }
  return conditionalRequiredRules;
}

function getConditionalProhibitedRules(
  oneOf: unknown[],
  definitions: Record<string, unknown>,
  nestedFieldName: string
): ConditionalRule[] {
  const nestedFieldNamesPerBranch: Record<string, string[]> = {};

  for (const oneOfEntry of oneOf) {
    const data = getDataFromOneOfEntry(oneOfEntry);
    if (!data) {
      continue;
    }
    const { propertyKey, propertyRef } = data;

    const nestedFieldNames = getNestedFieldNames(
      definitions,
      propertyRef,
      `${nestedFieldName}-${propertyKey}`
    );
    nestedFieldNamesPerBranch[propertyKey] = nestedFieldNames;
  }

  const conditionalProhibitedRules: ConditionalRule[] = [];

  for (const [propertyKey, nestedFieldNames] of Object.entries(
    nestedFieldNamesPerBranch
  )) {
    // The condition for applying the prohibited rules is that at least one field in another branch is set.
    const fieldNamesOfOtherBranches: string[] = Object.entries(
      nestedFieldNamesPerBranch
    )
      .filter(([otherPropertyKey]) => otherPropertyKey !== propertyKey)
      .flatMap(([, otherNestedFieldNames]) => otherNestedFieldNames);

    const presentConditions: Condition[] = fieldNamesOfOtherBranches.map(
      (fieldName) => ({
        field: fieldName,
        type: 'present',
        value: true,
      })
    );

    const prohibitedRules: ProhibitedRule[] = nestedFieldNames.map(
      (fieldName) => ({
        id: `generated:${fieldName}-prohibited-conditional`,
        description: `${fieldName} is prohibited if any of the fields in the other 'oneOf' branches are present`,
        type: 'prohibited',
        value: true,
        target: fieldName,
      })
    );

    const conditionalProhibitedRulesForBranch: ConditionalRule[] =
      presentConditions.map((condition) => ({
        id: `generated:${nestedFieldName}-${propertyKey}-conditional-prohibited`,
        description: `${nestedFieldName}-${propertyKey} is prohibited if ${condition.field} is present`,
        type: 'condition',
        conditions: [condition],
        rules: prohibitedRules,
      }));

    conditionalProhibitedRules.push(...conditionalProhibitedRulesForBranch);
  }
  return conditionalProhibitedRules;
}

function getDataFromOneOfEntry(oneOfEntry: unknown):
  | {
      propertyKey: string;
      propertyRef: string;
      propertyName: string | undefined;
    }
  | undefined {
  if (!isValidObject(oneOfEntry)) {
    throw new Error(
      `Invalid JSON schema: '${oneOfEntry}' is not a valid object`
    );
  }

  const properties = oneOfEntry['properties'];
  if (!isValidObject(properties) || Object.keys(properties).length !== 1) {
    throw new Error(`Invalid properties format in oneOf entry: ${oneOfEntry}`);
  }

  const [propertyKey, propertyValue] = Object.entries(properties)[0];

  if (!isValidObject(propertyValue)) {
    throw new Error(`Invalid property format for "${propertyKey}".`);
  }

  const propertyRef = getRef(propertyValue);
  if (!propertyRef) {
    return undefined;
  }
  const propertyName = getName(propertyValue);

  return {
    propertyKey,
    propertyRef,
    propertyName,
  };
}

function getPrimitiveRules(
  type: unknown,
  definition: Record<string, unknown>,
  fieldName: string,
  nestedFieldName: string,
  required: boolean,
  includeOnlyRequiredRules = false
): Rule[] {
  const rules: Rule[] = [];

  if (!(type === 'string' || type === 'boolean')) {
    console.error(
      `Invalid JSON schema: '${fieldName}' is of type ${type}. This is not supported as of now.`
    );
    return rules;
  }

  if (type === 'string') {
    const minLength = definition['minLength'];
    const maxLength = definition['maxLength'];
    const pattern = definition['pattern'];
    const enumValues = definition['enum'];

    let addedRequiredRule = false;

    if (minLength) {
      if (minLength !== 1) {
        console.error(
          `Invalid JSON schema: '${fieldName}' has a minLength of ${minLength}, but only minLength of 1 is supported.`
        );
      } else if (required && !addedRequiredRule) {
        rules.push(getRequiredRule(fieldName, nestedFieldName));
        addedRequiredRule = true;
      }
    }

    if (maxLength) {
      if (typeof maxLength !== 'number') {
        console.error(
          `Invalid JSON schema: '${fieldName}' has a maxLength of ${definition['maxLength']}, but only numeric maxLength is supported.`
        );
      } else if (!includeOnlyRequiredRules) {
        rules.push({
          id: `generated:${nestedFieldName}-maxLength`,
          description: `${fieldName} must be at most ${definition['maxLength']} characters long.`,
          type: 'maxLength',
          value: maxLength,
          target: nestedFieldName,
        });
      }
    }

    if (pattern) {
      if (typeof pattern !== 'string') {
        console.error(
          `Invalid JSON schema: '${fieldName}' has a pattern of ${pattern}, but only string patterns are supported.`
        );
      } else {
        if (!includeOnlyRequiredRules) {
          rules.push(getPatternRule(fieldName, nestedFieldName, pattern));
        }
        // This is an assumption we make: If a pattern is defined and the element is required, the field is required, even though the pattern may allow for the empty string.
        if (required && !addedRequiredRule) {
          rules.push(getRequiredRule(fieldName, nestedFieldName));
          addedRequiredRule = true;
        }
      }
    }

    if (enumValues) {
      if (
        !Array.isArray(enumValues) ||
        !enumValues.every((v) => typeof v === 'string')
      ) {
        console.error(
          `Invalid JSON schema: '${fieldName}' has an enum of ${enumValues}, but only string array enums are supported.`
        );
      } else {
        if (!includeOnlyRequiredRules) {
          rules.push(
            getPatternRule(fieldName, nestedFieldName, undefined, enumValues)
          );
        }
        if (required && !addedRequiredRule) {
          rules.push(getRequiredRule(fieldName, nestedFieldName));
          addedRequiredRule = true;
        }
      }
    }
  } else if (definition['type'] === 'boolean') {
    const enumValues = ['true', 'false'];
    if (!includeOnlyRequiredRules) {
      rules.push(
        getPatternRule(fieldName, nestedFieldName, undefined, enumValues)
      );
    }
    if (required) {
      rules.push(getRequiredRule(fieldName, nestedFieldName));
    }
  }

  return rules;
}

function getRules(
  definitions: Record<string, unknown>,
  ref: string,
  fieldName: string,
  nestedFieldName: string,
  globalRequired: boolean,
  includeOnlyRequiredRules: boolean
): Rule[] {
  const definition = definitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid JSON schema: '${ref}' is not a valid object`);
  }

  const rules: Rule[] = [];

  if (definition['type'] === 'string' || definition['type'] === 'boolean') {
    rules.push(
      ...getPrimitiveRules(
        definition['type'],
        definition,
        fieldName,
        nestedFieldName,
        globalRequired,
        includeOnlyRequiredRules
      )
    );
  } else if (definition['type'] === 'array') {
    throw new Error(
      `Array type on definition level is not supported. Error at field ${nestedFieldName}.`
    );
  } else if (definition['type'] === 'object') {
    const properties = definition['properties'];
    const oneOf = definition['oneOf'];

    if (!properties && !oneOf) {
      return rules;
    }

    const nestedRules: Rule[] = [];

    if (isValidObject(properties)) {
      const requiredProperties = definition['required'];
      if (
        !(typeof requiredProperties === 'undefined') &&
        !Array.isArray(requiredProperties)
      ) {
        throw new Error(
          `Invalid required properties format for "${fieldName}". Expected undefined or an array.`
        );
      }

      for (const [propertyKey, propertyValue] of Object.entries(properties)) {
        if (!isValidObject(propertyValue)) {
          throw new Error(
            `Invalid JSON schema: '${propertyKey}' is not a valid object`
          );
        }

        const propertyRef = getRef(propertyValue);
        const propertyName = getName(propertyValue);

        // For going one level deeper, we need to concatenate the field name for the next level and determine whether the next level is still required.
        const oneDeeperFieldName = `${nestedFieldName}-${propertyKey}`;

        const oneDeeperGloballyRequired =
          globalRequired &&
          (requiredProperties?.includes(propertyKey) ?? false);

        if (!propertyRef) {
          // There might be a primitive rule defined here that is not extracted into its own definition.
          nestedRules.push(
            ...getPrimitiveRules(
              propertyValue['type'],
              propertyValue,
              propertyName ?? propertyKey,
              oneDeeperFieldName,
              oneDeeperGloballyRequired,
              includeOnlyRequiredRules
            )
          );
          continue;
        }

        if ('type' in propertyValue) {
          if (propertyValue['type'] !== 'array') {
            throw new Error(
              `Invalid JSON schema: '${propertyKey}' is of type ${propertyValue['type']} and there is an additional ref to ${propertyRef}. This is not supported as of now.`
            );
          }

          // There are different rules that may be applied in this case:
          // MaxItemsRule in case 'maxItems' is defined
          // Nested rules excluding RequiredRules for individual items' fields (required is only necessary for the first item in the array)
          // RequiredRules for the first item's fields in case the array is required and the fields themselves are required.

          // MaxItemsRule
          const maxItems = propertyValue['maxItems'];
          if (maxItems) {
            if (typeof maxItems !== 'number') {
              throw new Error(
                `Invalid JSON schema: '${propertyKey}' has a maxItems of ${maxItems}, but only numeric maxItems is supported.`
              );
            }

            if (!includeOnlyRequiredRules) {
              nestedRules.push({
                id: `${oneDeeperFieldName}-maxItems`,
                description: `${propertyKey} must have at most ${maxItems} items.`,
                type: 'maxItems',
                value: maxItems,
                target: oneDeeperFieldName,
              });
            }
          }

          // Nested rules excluding RequiredRules for individual items' fields
          nestedRules.push(
            ...getRules(
              definitions,
              propertyRef,
              propertyName ?? propertyKey,
              oneDeeperFieldName,
              false, // Only the first item in the array (and thus its fields) can be required. This is done below.
              includeOnlyRequiredRules
            )
          );

          if (oneDeeperGloballyRequired) {
            // RequiredRules for the first item's fields
            nestedRules.push(
              ...getRules(
                definitions,
                propertyRef,
                propertyName ?? propertyKey,
                `${oneDeeperFieldName}-0`, // Signify that the rule is for the first item in the array
                true, // The first item's fields are required if the array itself is required.
                true
              )
            );
          }
        } else {
          // Standard case: no 'type' defined, but a ref to another definition.
          nestedRules.push(
            ...getRules(
              definitions,
              propertyRef,
              propertyName ?? propertyKey,
              oneDeeperFieldName,
              oneDeeperGloballyRequired,
              includeOnlyRequiredRules
            )
          );
        }
      }

      // Add conditional required rules for all required properties if the current level is not globally required (for each required property, there must be a rule that requires it if any of the other fields are set). In the 'globally required' case, we create the basic required rules directly above.
      if (!globalRequired && requiredProperties && !includeOnlyRequiredRules) {
        for (const requiredPropertyKey of requiredProperties) {
          const requiredProperty = properties[requiredPropertyKey];
          if (!isValidObject(requiredProperty)) {
            throw new Error(
              `Invalid JSON schema: '${requiredPropertyKey}' is not a valid object`
            );
          }
          const otherProperties = Object.entries(properties).filter(
            ([key]) => key !== requiredPropertyKey
          );
          const otherNestedFieldNames = otherProperties.reduce<string[]>(
            (acc, [propertyKey, propertyValue]) => {
              if (!isValidObject(propertyValue)) {
                throw new Error(
                  `Invalid JSON schema: '${propertyKey}' is not a valid object`
                );
              }
              const propertyNestedFieldName = `${nestedFieldName}-${propertyKey}`;
              const propertyRef = getRef(propertyValue);
              if (!propertyRef) {
                acc.push(propertyNestedFieldName);
                return acc;
              }
              acc.push(
                ...getNestedFieldNames(
                  definitions,
                  propertyRef,
                  propertyNestedFieldName
                )
              );
              return acc;
            },
            []
          );
          const requiredPropertyRef = getRef(requiredProperty);
          const requiredPropertyName =
            getName(requiredProperty) ?? requiredPropertyKey;
          const requiredPropertyNestedFieldName = `${nestedFieldName}-${requiredPropertyKey}`;
          const requiredRules: RequiredRule[] = !requiredPropertyRef
            ? [
                getRequiredRule(
                  requiredPropertyName,
                  requiredPropertyNestedFieldName
                ),
              ]
            : getRules(
                definitions,
                requiredPropertyRef,
                requiredPropertyName,
                requiredPropertyNestedFieldName,
                true,
                true
              ).filter((rule) => rule.type === 'required');
          // To make sure that ids stay unique, we add the id of the parent conditional rule to the ids of all conditional required rules.
          const conditionalParentId = `${nestedFieldName}-${requiredPropertyKey}-conditional-required`;
          for (const rule of requiredRules) {
            rule.id = addBaseKey(rule.id, conditionalParentId, '_');
          }
          nestedRules.push({
            id: `generated:${conditionalParentId}`,
            description: `If any of the other fields are set, then ${requiredPropertyKey} is required.`,
            type: 'condition',
            conditions: otherNestedFieldNames.map((fieldName) => ({
              field: fieldName,
              type: 'present',
              value: true,
            })),
            conditionsConnector: 'or',
            rules: requiredRules,
          });
        }
      }
    } else if (Array.isArray(oneOf)) {
      for (const oneOfEntry of oneOf) {
        const data = getDataFromOneOfEntry(oneOfEntry);

        if (!data) {
          continue;
        }

        const { propertyKey, propertyRef, propertyName } = data;

        nestedRules.push(
          ...getRules(
            definitions,
            propertyRef,
            propertyName ?? propertyKey,
            `${nestedFieldName}-${propertyKey}`,
            false, // 'oneOf' always means that the sub properties are not globally required. There might be a conditional rule however, that requires one option.
            includeOnlyRequiredRules
          )
        );
      }

      if (globalRequired) {
        // We need to construct conditional 'required' rules for the oneOf entries, requiring all required fields on one branch of the tree if all fields in the other branches are not set.
        nestedRules.push(
          ...getConditionalRequiredRules(oneOf, definitions, nestedFieldName)
        );
      }

      if (!includeOnlyRequiredRules) {
        // We need to construct conditional 'prohibited' rules for the oneOf entries, prohibiting all fields on one branch of the tree if at least one field in another branch is set.
        // Note: This produces a lot of rules and is not strictly necessary. If we run into performance issues, we can consider removing this.
        // nestedRules.push(
        //   ...getConditionalProhibitedRules(oneOf, definitions, nestedFieldName)
        // );
      }
    }

    rules.push(...nestedRules);
  } else {
    console.error(
      `Invalid JSON schema: '${fieldName}' is of type ${definition['type']}. This is not supported as of now.`
    );
  }

  return rules;
}

export function generateRules(
  jsonSchema: Record<string, unknown>,
  basePropertyAbbreviatedName: string
): Rule[] {
  if (!('properties' in jsonSchema && 'definitions' in jsonSchema)) {
    throw new Error(
      "Invalid JSON schema: 'properties' not 'definitions' not found"
    );
  }

  const properties = jsonSchema['properties'];
  if (!isValidObject(properties)) {
    throw new Error("Invalid JSON schema: 'properties' is not a valid object");
  }

  const definitions = jsonSchema['definitions'];
  if (!isValidObject(definitions)) {
    throw new Error("Invalid JSON schema: 'definitions' is not a valid object");
  }

  const baseProperty = properties[basePropertyAbbreviatedName];
  if (!isValidObject(baseProperty)) {
    throw new Error(
      `Invalid JSON schema: '${basePropertyAbbreviatedName}' is not a valid object`
    );
  }

  const basePropertyName = getName(baseProperty);

  const basePropertyRef = getRef(baseProperty);
  if (!basePropertyRef) {
    throw new Error(
      `Invalid JSON schema: '${basePropertyAbbreviatedName}' does not have a valid reference`
    );
  }

  return getRules(
    definitions,
    basePropertyRef,
    basePropertyName ?? basePropertyAbbreviatedName,
    basePropertyAbbreviatedName,
    true,
    false
  );
}
