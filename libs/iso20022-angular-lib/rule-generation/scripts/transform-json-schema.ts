import { XsdElement } from './types';
import {
  getRef,
  isValidObject,
  isValidStringArray,
  toSnakeCase,
} from './utils';

function getMergedDefinition(
  definitions: Record<string, unknown>[],
  ref: string
): Record<string, unknown> {
  const { properties, oneOf, required, ...rest } = definitions[0];

  const mergedProperties: Record<string, Record<string, unknown>> = {};
  const mergedOneOf: Record<string, unknown>[] = [];

  for (const definition of definitions) {
    const definitionProperties = definition['properties'];
    const definitionOneOf = definition['oneOf'];
    if (isValidObject(definitionProperties)) {
      for (const [propertyKey, propertyValue] of Object.entries(
        definitionProperties
      )) {
        if (!isValidObject(propertyValue)) {
          throw new Error(`Invalid property format for "${propertyKey}".`);
        }

        const existingProperty = mergedProperties[propertyKey];
        if (existingProperty) {
          // Merge nestedFieldNames
          existingProperty['nestedFieldNames'] = [
            ...(isValidStringArray(existingProperty['nestedFieldNames'])
              ? existingProperty['nestedFieldNames']
              : []),
            ...(isValidStringArray(propertyValue['nestedFieldNames'])
              ? propertyValue['nestedFieldNames']
              : []),
          ];
        } else {
          mergedProperties[propertyKey] = {
            ...propertyValue,
          };
        }
      }
    }
    if (Array.isArray(definitionOneOf)) {
      for (const oneOfEntry of definitionOneOf) {
        if (
          !isValidObject(oneOfEntry) ||
          !isValidObject(oneOfEntry['properties'])
        ) {
          throw new Error(`Invalid oneOf entry: ${oneOfEntry}`);
        }
        const propertyKey = Object.keys(oneOfEntry['properties'])[0];
        if (!propertyKey) {
          throw new Error(
            `No property key found in oneOf entry: ${oneOfEntry}`
          );
        }
        if (!isValidObject(oneOfEntry['properties'][propertyKey])) {
          throw new Error(
            `Invalid property format for "${propertyKey}" in oneOf entry.`
          );
        }

        const existingOneOf = mergedOneOf.find(
          (entry) =>
            isValidObject(entry['properties']) &&
            Object.keys(entry['properties'])[0] === propertyKey
        );

        if (!existingOneOf) {
          mergedOneOf.push(oneOfEntry);
        } else {
          if (
            !isValidObject(existingOneOf['properties']) ||
            !isValidObject(existingOneOf['properties'][propertyKey])
          ) {
            throw new Error(
              `Invalid existing oneOf entry for property "${propertyKey}".`
            );
          }
          let mergedNestedFieldNames =
            existingOneOf['properties'][propertyKey]['nestedFieldNames'];
          if (!isValidStringArray(mergedNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in existing oneOf entry.`
            );
          }

          const newNestedFieldNames =
            oneOfEntry['properties'][propertyKey]['nestedFieldNames'];
          if (!isValidStringArray(newNestedFieldNames)) {
            throw new Error(
              `Invalid nestedFieldNames for property "${propertyKey}" in new oneOf entry.`
            );
          }

          // Merge nestedFieldNames
          mergedNestedFieldNames = [
            ...mergedNestedFieldNames,
            ...newNestedFieldNames,
          ];

          existingOneOf['properties'][propertyKey]['nestedFieldNames'] =
            mergedNestedFieldNames;
        }
      }
    }
  }

  const mergedDefinition: Record<string, unknown> = {
    ...rest,
    properties:
      Object.keys(mergedProperties).length > 0 ? mergedProperties : undefined,
    oneOf: mergedOneOf.length > 0 ? mergedOneOf : undefined,
    required,
  };

  return mergedDefinition;
}

function getTransformedProperty(
  originalPropertyKey: string,
  originalProperty: unknown,
  nameMappingChild: XsdElement | undefined,
  parentNestedAbbrName: string
): Record<string, unknown> {
  if (!isValidObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalPropertyKey}".`);
  }

  return {
    ...originalProperty,
    name: nameMappingChild ? nameMappingChild.fullName : originalPropertyKey,
    nestedFieldNames: [
      nameMappingChild
        ? nameMappingChild.nestedAbbrName
        : `${parentNestedAbbrName}-${originalPropertyKey}`,
    ],
  };
}

function getTransformedProperties(
  originalProperties: Record<string, unknown>,
  originalRequiredProperties: unknown,
  nameMappings: XsdElement
): {
  properties: Record<string, Record<string, unknown>>;
  requiredProperties: string[] | undefined;
} {
  if (
    !(typeof originalRequiredProperties === 'undefined') &&
    !Array.isArray(originalRequiredProperties)
  ) {
    throw new Error(
      `Invalid required properties format for "${nameMappings.fullName}". Expected undefined or an array.`
    );
  }

  const transformedProperties: Record<string, Record<string, unknown>> = {};
  const transformedRequiredProperties: string[] = [];

  for (const [propertyKey, propertyValue] of Object.entries(
    originalProperties
  )) {
    const nameMappingChild = nameMappings.children.find(
      (child) => toSnakeCase(child.fullName) === propertyKey
    );

    if (!nameMappingChild) {
      console.log(
        `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}". This is fine for 'simpleContent' XSD entries where "${propertyKey}" is an extension. We just keep the original entry in that case.`
      );
    }

    const transformedProperty = getTransformedProperty(
      propertyKey,
      propertyValue,
      nameMappingChild,
      nameMappings.nestedAbbrName
    );

    const finalPropertyKey =
      nameMappingChild && nameMappingChild.abbrName
        ? nameMappingChild.abbrName
        : propertyKey;

    transformedProperties[finalPropertyKey] = transformedProperty;
    if (
      originalRequiredProperties &&
      originalRequiredProperties.includes(propertyKey)
    ) {
      transformedRequiredProperties.push(finalPropertyKey);
    }
  }

  return {
    properties: transformedProperties,
    requiredProperties:
      transformedRequiredProperties.length > 0
        ? transformedRequiredProperties
        : undefined,
  };
}

function getTransformedDefinition(
  originalDefinition: Record<string, unknown>,
  properties: unknown,
  oneOf: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  const transformedDefinition: Record<string, unknown> = {
    ...originalDefinition,
  };

  if (isValidObject(properties)) {
    const requiredProperties = originalDefinition['required'];
    const transformedProperties = getTransformedProperties(
      properties,
      requiredProperties,
      nameMappings
    );

    transformedDefinition['properties'] = transformedProperties.properties;
    transformedDefinition['required'] =
      transformedProperties.requiredProperties;
  }

  if (Array.isArray(oneOf)) {
    const transformedOneOf: Record<string, unknown>[] = [];
    for (const oneOfEntry of oneOf) {
      const oneOfProperties = oneOfEntry.properties;
      if (
        !isValidObject(oneOfProperties) ||
        Object.keys(oneOfProperties).length !== 1
      ) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const oneOfRequiredProperties = oneOfEntry.required;

      const transformedProperties = getTransformedProperties(
        oneOfProperties,
        oneOfRequiredProperties,
        nameMappings
      );

      transformedOneOf.push({
        ...oneOfEntry,
        properties: transformedProperties.properties,
        required: transformedProperties.requiredProperties,
      });
    }
    transformedDefinition['oneOf'] = transformedOneOf;
  }

  return transformedDefinition;
}

function getNestedTransformedDefinitionsForProperty(
  originalDefinitions: Record<string, unknown>,
  propertyKey: string,
  originalProperty: unknown,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  if (!isValidObject(originalProperty)) {
    throw new Error(`Invalid property format for "${originalProperty}".`);
  }

  const propertyRef = getRef(originalProperty);

  if (!propertyRef) {
    return new Map();
  }

  const nameMappingChild = nameMappings.children.find(
    (child) => toSnakeCase(child.fullName) === propertyKey
  );

  if (!nameMappingChild) {
    throw new Error(
      `No name mapping found for "${propertyKey}" in "${nameMappings.fullName}".`
    );
  }

  return getTransformedDefinitions(
    originalDefinitions,
    propertyRef,
    nameMappingChild
  );
}

function getTransformedDefinitions(
  originalDefinitions: Record<string, unknown>,
  ref: string,
  nameMappings: XsdElement
): Map<string, Array<Record<string, unknown>>> {
  const definition = originalDefinitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid definition for "${ref}".`);
  }

  const properties = definition['properties'];
  const oneOf = definition['oneOf'];

  if (!properties && !oneOf) {
    return new Map([[ref, [definition]]]);
  }

  const transformedDefinitionsMap: Map<
    string,
    Array<Record<string, unknown>>
  > = new Map();

  // Transform the definition itself without going through refs
  const transformedDefinition = getTransformedDefinition(
    definition,
    properties,
    oneOf,
    nameMappings
  );

  transformedDefinitionsMap.set(ref, [transformedDefinition]);

  // Go through the refs in 'properties' or 'oneOf' and transform them recursively

  if (isValidObject(properties)) {
    for (const [propertyKey, propertyValue] of Object.entries(properties)) {
      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  if (Array.isArray(oneOf)) {
    for (const oneOfEntry of oneOf) {
      const properties = oneOfEntry.properties;
      if (!isValidObject(properties) || Object.keys(properties).length !== 1) {
        throw new Error(
          `Invalid properties format in oneOf entry: ${oneOfEntry}`
        );
      }

      const [propertyKey, propertyValue] = Object.entries(properties)[0];

      const nestedTransformedDefinitions =
        getNestedTransformedDefinitionsForProperty(
          originalDefinitions,
          propertyKey,
          propertyValue,
          nameMappings
        );

      for (const [key, entries] of nestedTransformedDefinitions) {
        if (!transformedDefinitionsMap.has(key)) {
          transformedDefinitionsMap.set(key, []);
        }
        transformedDefinitionsMap.get(key)?.push(...entries);
      }
    }
  }

  return transformedDefinitionsMap;
}

export function transformJsonSchema(
  jsonSchema: unknown,
  nameMappings: XsdElement
): Record<string, unknown> {
  if (
    !isValidObject(jsonSchema) ||
    !('properties' in jsonSchema) ||
    !('definitions' in jsonSchema)
  ) {
    throw new Error('Invalid JSON schema format.');
  }

  const { properties, definitions, ...rest } = jsonSchema;

  if (!isValidObject(definitions)) {
    throw new Error('Invalid definitions format.');
  }

  const basePropertyKey = toSnakeCase(nameMappings.fullName);

  if (!isValidObject(properties) || !(basePropertyKey in properties)) {
    throw new Error("Invalid 'properties' format.");
  }

  const baseProperty = properties[basePropertyKey];

  const transformedBaseProperty = getTransformedProperty(
    basePropertyKey,
    baseProperty,
    nameMappings,
    ''
  );

  const transformedProperties: Record<string, unknown> = {
    ...properties,
    [nameMappings.abbrName]: transformedBaseProperty,
  };

  delete transformedProperties[basePropertyKey];

  const basePropertyRef = getRef(transformedBaseProperty);
  if (!basePropertyRef) {
    throw new Error(
      `Invalid base property reference for "${basePropertyKey}".`
    );
  }

  const transformedDefinitions: Map<
    string,
    Array<Record<string, unknown>>
  > = getTransformedDefinitions(definitions, basePropertyRef, nameMappings);

  // The resulting transformedDefinitions contains multiple entries for the same ref if a definition is used in more than one branch.
  // We need to merge them into a single definition per ref combining the different 'nestedFieldName's.
  const mergedDefinitions: Record<string, Record<string, unknown>> = {};
  for (const [ref, entries] of transformedDefinitions) {
    const mergedDefinition: Record<string, unknown> = getMergedDefinition(
      entries,
      ref
    );
    mergedDefinitions[ref] = mergedDefinition;
  }

  const transformedSchema: Record<string, unknown> = {
    ...rest,
    properties: transformedProperties,
    definitions: mergedDefinitions,
  };

  return transformedSchema;
}
