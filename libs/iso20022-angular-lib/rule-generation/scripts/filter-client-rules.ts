import {
  ConditionalRule,
  Rule,
  ServerOnlyRule,
} from '@helaba/iso20022-lib/rules';

export function filterClientRules(allRules: Rule[]): Rule[] {
  const serverOnlyRules: ServerOnlyRule[] = allRules.filter(
    (rule) => rule.type === 'serverOnly'
  );
  const serverOnlyFields: string[] = serverOnlyRules
    .filter((rule) => rule.value === true)
    .map((rule) => rule.target);

  const filteredNonConditionalRules = allRules.filter(
    (rule) =>
      rule.type !== 'condition' && !serverOnlyFields.includes(rule.target)
  );

  // Remove nested rules from conditional rules that target server-only fields
  const conditionalRules = allRules.filter((rule) => rule.type === 'condition');
  const filteredConditionalRules = conditionalRules.reduce<ConditionalRule[]>(
    (acc, rule) => {
      const filteredRules = rule.rules.filter(
        (r) => !serverOnlyFields.includes(r.target)
      );
      // If there are no nested rules left after filtering, we remove the conditional rule entirely
      if (filteredRules.length > 0) {
        acc.push({
          ...rule,
          rules: filteredRules,
        });
      }
      return acc;
    },
    []
  );

  return [...filteredNonConditionalRules, ...filteredConditionalRules];
}
