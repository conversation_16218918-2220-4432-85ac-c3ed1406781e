import { Rule } from '@helaba/iso20022-lib/rules';

export function validateRules(rules: Rule[]): void {
  // Make sure all ids are unique
  const ids = new Set<string>();
  for (const rule of rules) {
    if (ids.has(rule.id)) {
      throw new Error(`Duplicate rule id found: ${rule.id}`);
    }
    ids.add(rule.id);

    if (rule.type === 'condition') {
      for (const conditionalRule of rule.rules) {
        if (ids.has(conditionalRule.id)) {
          throw new Error(
            `Duplicate rule id found in conditional rules: ${conditionalRule.id}`
          );
        }
        ids.add(conditionalRule.id);
      }
    }
  }
}
