import { sharedRequiredDefinitions } from '../input/pacs.008.001.08_cbprplus/custom_rules';
import {
  addBaseKey,
  getNestedFieldNames,
  getRef,
  isValidObject,
  isValidStringArray,
} from './utils';
import {
  Condition,
  Rule,
  NestedCondition,
  isCondition,
  CustomRule,
  BasicRule,
  RequiredRule,
  ConditionalRule,
  ProhibitedRule,
  RequiredDefinition,
} from '../../projects/iso20022-lib/rules';

function addBaseKeyToRule<T extends Rule>(rule: T, baseKey: string): T {
  const transformedRule = { ...rule };

  if ('baseKeys' in transformedRule) {
    // Remove the baseKeys property as it is not needed in the transformed rule.
    delete transformedRule.baseKeys;
  }

  if (transformedRule.id) {
    transformedRule.id = addBaseKey(transformedRule.id, baseKey, '-');
  }
  if ('target' in transformedRule && transformedRule.target) {
    transformedRule.target = addBaseKey(transformedRule.target, baseKey, '-');
  }

  return transformedRule;
}

function addBaseKeyToCondition(
  condition: Condition,
  baseKey: string
): Condition {
  const transformed: Condition = { ...condition };

  transformed.field = addBaseKey(transformed.field, baseKey, '-');

  if ('otherField' in transformed && transformed.otherField) {
    transformed.otherField = addBaseKey(transformed.otherField, baseKey, '-');
  }

  return transformed;
}

function addBaseKeyToNestedCondition(
  condition: NestedCondition,
  baseKey: string
): NestedCondition {
  if (isCondition(condition)) {
    return addBaseKeyToCondition(condition, baseKey);
  } else {
    const transformed: NestedCondition = { ...condition };
    transformed.conditions = transformed.conditions.map((cond: Condition) =>
      addBaseKeyToCondition(cond, baseKey)
    );

    return transformed;
  }
}

/**
 * For each base key, create a new rule with the base key added to all 'id', 'target', 'field', 'otherField', and 'errorMessage' properties.
 * @param rule the original rule with the 'baseKeys' property
 * @returns an array of new rules with the base keys applied
 */
function getRulesForBaseKeys(rule: CustomRule): Rule[] {
  if (!rule.baseKeys || rule.baseKeys.length === 0) {
    return [rule];
  }

  return rule.baseKeys.map((baseKey: string) => {
    const transformedRule: Rule = addBaseKeyToRule<Rule>(rule, baseKey);

    if (
      'conditions' in transformedRule &&
      transformedRule.conditions &&
      transformedRule.conditions.length > 0
    ) {
      transformedRule.conditions = transformedRule.conditions.map(
        (condition: NestedCondition) =>
          addBaseKeyToNestedCondition(condition, baseKey)
      );
    }

    if (
      'rules' in transformedRule &&
      transformedRule.rules &&
      transformedRule.rules.length > 0
    ) {
      transformedRule.rules = transformedRule.rules.map((subRule: BasicRule) =>
        addBaseKeyToRule<BasicRule>(subRule, baseKey)
      );
    }

    return transformedRule;
  });
}

function getTargetRef(
  target: string,
  definitions: Record<string, unknown>
): string | undefined {
  const targetRefs: string[] = Object.values(definitions).reduce<string[]>(
    (acc, definition) => {
      if (!isValidObject(definition) || !('properties' in definition)) {
        return acc;
      }
      const properties = definition['properties'];
      if (!isValidObject(properties)) {
        return acc;
      }

      for (const propertyKey of Object.keys(properties)) {
        const property = properties[propertyKey];

        if (!isValidObject(property) || !('nestedFieldNames' in property)) {
          continue;
        }

        const nestedFieldNames = property['nestedFieldNames'];

        if (
          isValidStringArray(nestedFieldNames) &&
          nestedFieldNames.includes(target)
        ) {
          const ref = getRef(property);
          if (ref) {
            acc.push(ref);
          }
        }
      }

      return acc;
    },
    []
  );

  if (targetRefs.length === 0) {
    return undefined;
  }
  if (targetRefs.length > 1) {
    throw new Error(
      `Target "${target}" is referenced in multiple definitions: ${targetRefs.join(
        ', '
      )}.`
    );
  }
  return targetRefs[0];
}

function getDefinition(
  ref: string,
  definitions: Record<string, unknown>
): Record<string, unknown> | undefined {
  const definition = definitions[ref];
  if (!isValidObject(definition) || !('type' in definition)) {
    throw new Error(`Definition for ref "${ref}" is not a valid object.`);
  }

  if (definition['type'] !== 'object') {
    return undefined;
  }

  return definition;
}

function insertKeySuffix(
  original: string,
  newKeySuffix: string,
  originalSuffix: string
) {
  if (!original.endsWith(originalSuffix)) {
    throw new Error(
      `Original key "${original}" does not end with the suffix "${originalSuffix}".`
    );
  }

  const baseKey = original.slice(0, original.length - originalSuffix.length);

  return `${baseKey}-${newKeySuffix}${originalSuffix}`;
}

/**
 * Retrieve a map that contains for each required rule id the target reference (e.g. 'PostalAddress24__1').
 */
function getRelevantDefinitions(
  conditionalRequiredRules: RequiredRule[],
  definitions: Record<string, unknown>
): Map<string, string> {
  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalRequiredRule of conditionalRequiredRules) {
    const target = conditionalRequiredRule.target;
    const targetRef = getTargetRef(target, definitions);
    if (!targetRef) {
      // This means that this rule does not have to be expanded, it has no ref and thus there is no further nesting.
      continue;
    }

    const nestedDefinition = getDefinition(targetRef, definitions);
    if (!nestedDefinition) {
      // This means that this rule does not have to be expanded, the rule is not of type "object" and thus there is no further nesting.
      continue;
    }

    if (!(targetRef in sharedRequiredDefinitions)) {
      throw new Error(
        `Target "${target}" is not defined in sharedRequiredDefinitions.`
      );
    }

    relevantDefinitions.set(conditionalRequiredRule.id, targetRef);
  }

  return relevantDefinitions;
}

function getExpandedRequiredRule(
  conditionalRequiredRule: RequiredRule,
  expansion: string
): RequiredRule {
  return {
    ...conditionalRequiredRule,
    id: insertKeySuffix(
      conditionalRequiredRule.id,
      expansion,
      '-required-conditional'
    ),
    description: `At least one occurrence of the element ${expansion} must be present`,
    target: insertKeySuffix(conditionalRequiredRule.target, expansion, ''),
  };
}

function getRequiredRulesForAnd(
  and: string[],
  conditionalRequiredRule: RequiredRule
): RequiredRule[] {
  return and.map((andEntry: string) => {
    return getExpandedRequiredRule(conditionalRequiredRule, andEntry);
  });
}

function getRequiredRulesForOr(
  or: (string | string[])[],
  conditionalRequiredRule: RequiredRule
): RequiredRule[] {
  return or.map((orEntry: string | string[]) => {
    // If the entry is an array (denoting an "and" condition), only take the first value. The remaining values will be handled in a separate conditional rule.
    const orKey = Array.isArray(orEntry) ? orEntry[0] : orEntry;

    return getExpandedRequiredRule(conditionalRequiredRule, orKey);
  });
}

/**
 * Go through the "or" array and check if there are any entries that are arrays (denoting an "and" condition).
 * E.g. "or: ['field1', ['field2', 'field3']]" - this means that either 'field1' must be present or both 'field2' and 'field3' must be present.
 * Only the first value from the "and" array was taken. Now define a further conditional rule that requires all other fields from the "and" array as soon as the first value is present.
 */
function getAdditionalConditionalRules(
  or: (string | string[])[],
  originalConditionalRuleId: string,
  originalConditionalRequiredRule: RequiredRule
): ConditionalRule[] {
  const additionalRules: ConditionalRule[] = [];

  for (const orEntry of or) {
    if (!Array.isArray(orEntry) || orEntry.length <= 1) {
      // If the entry is not an array or has only one entry, we don't need to create a conditional rule.
      continue;
    }

    const additionalRequiredFields = orEntry.slice(1);
    const newConditionalRule: ConditionalRule = {
      id: insertKeySuffix(
        originalConditionalRuleId,
        additionalRequiredFields.join('-'),
        '-conditional-required'
      ),
      description: `If ${
        orEntry[0]
      } is present, then ${additionalRequiredFields.join(
        ', '
      )} must also be present.`,
      type: 'condition',
      conditions: [
        {
          type: 'present',
          value: true,
          field: insertKeySuffix(
            originalConditionalRequiredRule.target,
            orEntry[0],
            ''
          ),
        },
      ],
      rules: additionalRequiredFields.map((field) => ({
        id: insertKeySuffix(
          originalConditionalRequiredRule.id,
          field,
          '-required-conditional'
        ),
        description: `Required if ${orEntry[0]} is present.`,
        type: 'required',
        value: true,
        target: insertKeySuffix(
          originalConditionalRequiredRule.target,
          field,
          ''
        ),
      })),
      rulesConnector: 'and',
    };
    additionalRules.push(newConditionalRule);
  }

  return additionalRules;
}

/**
 * Expands a rule with type "condition" that has a "required" target into multiple rules based on the sharedRequiredDefinitions.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandRequiredTargets(
  rule: Rule,
  definitions: Record<string, unknown>
): Rule[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalRequiredRules = conditionalRules.filter(
    (rule: BasicRule) => rule.type === 'required'
  );

  const relevantDefinitions = getRelevantDefinitions(
    conditionalRequiredRules,
    definitions
  );

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  const targetRefs = conditionalRequiredRules.reduce<string[]>(
    (acc: string[], rule: RequiredRule) => {
      const targetRef = relevantDefinitions.get(rule.id);
      if (!targetRef) {
        return acc;
      }
      acc.push(targetRef);
      return acc;
    },
    []
  );
  const requiredDefinitions = targetRefs.map(
    (targetRef: string) => sharedRequiredDefinitions[targetRef]
  );

  if (
    !requiredDefinitions.every(
      (definition: RequiredDefinition) =>
        'and' in definition || 'or' in definition
    )
  ) {
    throw new Error(
      `No "and" or "or" entries found in a shared RequiredDefinition.`
    );
  }

  const expandedRules: ConditionalRule[] = [];

  const unaffectedConditionalRules = conditionalRules.filter(
    (r: BasicRule) => !relevantDefinitions.has(r.id)
  );
  let didExtractUnaffectedConditionalRules = false;

  if (
    rule.rulesConnector === 'or' &&
    requiredDefinitions.some(
      (definition: RequiredDefinition) => 'and' in definition
    )
  ) {
    throw new Error(
      `Rule ${rule.id} has a rulesConnector of "or" and there is there is a required definition with type "and" to be expanded. This is not supported as of now.`
    );
  } else if (
    (typeof rule.rulesConnector === 'undefined' ||
      rule.rulesConnector === 'and') &&
    conditionalRules.length > 1
  ) {
    // If it's just one conditional rule, the rulesConnector does not matter. If there is more than one conditional rule and all required definitions are of type "or" or all required definitions are of type "and", this is still not an issue. For the "or" case we just move out the original rules into their own rule.
    if (
      requiredDefinitions.some(
        (definition: RequiredDefinition) => 'or' in definition
      ) &&
      requiredDefinitions.some(
        (definition: RequiredDefinition) => 'and' in definition
      )
    ) {
      throw new Error(
        `Rule ${rule.id} has a rulesConnector of "and" and there is there is a mixture of required definitions with type "or" and "and" to be expanded. This is not supported as of now.`
      );
    } else if (
      requiredDefinitions.every(
        (definition: RequiredDefinition) => 'or' in definition
      )
    ) {
      // Move out the remaining rules to a new conditional rule with 'rulesConnector: "and"'
      const newConditionalRule: ConditionalRule = {
        ...rule,
        rules: unaffectedConditionalRules,
        rulesConnector: 'and',
      };
      expandedRules.push(newConditionalRule);
      didExtractUnaffectedConditionalRules = true;
    }
  }

  const expandedConditionalRequiredRules: RequiredRule[] = [];

  for (const ruleId of relevantDefinitions.keys()) {
    const conditionalRequiredRule = conditionalRequiredRules.find(
      (r: RequiredRule) => r.id === ruleId
    );
    if (!conditionalRequiredRule) {
      throw new Error(`No "required" rule found for rule ${rule.id}.`);
    }

    const targetRef = relevantDefinitions.get(ruleId);
    if (!targetRef) {
      throw new Error(`No targetRef found for rule ${rule.id}.`);
    }

    const requiredDefinition = sharedRequiredDefinitions[targetRef];

    if ('and' in requiredDefinition && requiredDefinition.and) {
      // Generate required rules for all entries in the "and" array.
      const and = requiredDefinition.and;
      const newConditionalRequiredRules: RequiredRule[] =
        getRequiredRulesForAnd(and, conditionalRequiredRule);
      expandedConditionalRequiredRules.push(...newConditionalRequiredRules);
    } else if ('or' in requiredDefinition && requiredDefinition.or) {
      // Generate required rules for all entries in the "or" array.
      const or = requiredDefinition.or;
      const newConditionalRequiredRules: RequiredRule[] = getRequiredRulesForOr(
        or,
        conditionalRequiredRule
      );
      expandedConditionalRequiredRules.push(...newConditionalRequiredRules);
      const additionalConditionalRules = getAdditionalConditionalRules(
        or,
        rule.id,
        conditionalRequiredRule
      );
      expandedRules.push(...additionalConditionalRules);
    }
  }

  const transformedRule = { ...rule };
  transformedRule.rules = didExtractUnaffectedConditionalRules
    ? [...expandedConditionalRequiredRules]
    : [...unaffectedConditionalRules, ...expandedConditionalRequiredRules];
  transformedRule.rulesConnector = requiredDefinitions.some(
    (definition: RequiredDefinition) => 'and' in definition
  )
    ? 'and'
    : 'or';
  expandedRules.push(transformedRule);

  return expandedRules;
}

/**
 * Expands a rule with type "condition" that has a "prohibited" target into multiple rules.
 * @param rule the rule to expand
 * @param jsonSchema the JSON schema to use for the expansion
 * @returns an array of rules that have been expanded from the original rule
 */
function expandProhibitedTargets(
  rule: Rule,
  definitions: Record<string, unknown>
): Rule[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditionalRules = rule.rules;
  const conditionalProhibitedRules = conditionalRules.filter(
    (rule: BasicRule) => rule.type === 'prohibited'
  );
  const conditionalNonProhibitedRules = conditionalRules.filter(
    (rule: BasicRule) => rule.type !== 'prohibited'
  );

  // We only allow 'rulesConnector: "and"' for conditional rules with "prohibited" rules.
  if (rule.rulesConnector === 'or' && conditionalProhibitedRules.length > 0) {
    throw new Error(
      `Rule ${rule.id} has a rulesConnector of "or" and there is a prohibited rule. This is not supported as of now.`
    );
  }

  const relevantDefinitions: Map<string, string> = new Map();

  for (const conditionalProhibitedRule of conditionalProhibitedRules) {
    const target = conditionalProhibitedRule.target;
    const targetRef = getTargetRef(target, definitions);
    if (!targetRef) {
      continue;
    }

    const nestedDefinition = getDefinition(targetRef, definitions);
    if (!nestedDefinition) {
      continue;
    }

    relevantDefinitions.set(target, targetRef);
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  const newConditionalProhibitedRules: ProhibitedRule[] = [];

  for (const [target, targetRef] of relevantDefinitions.entries()) {
    const prohibitedRuleToExpand = conditionalProhibitedRules.find(
      (rule: ProhibitedRule) => rule.target === target
    );

    if (!prohibitedRuleToExpand) {
      throw new Error(
        `No "prohibited" rule found for target "${target}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      target
    );

    newConditionalProhibitedRules.push(
      ...allNestedFieldNames.map(
        (fieldName): ProhibitedRule => ({
          ...prohibitedRuleToExpand,
          id: addBaseKey(
            prohibitedRuleToExpand.id,
            fieldName,
            '',
            '-prohibited-conditional',
            true
          ),
          target: fieldName,
        })
      )
    );
  }

  const conditionalProhibitedRulesNotToExpand =
    conditionalProhibitedRules.filter(
      (rule: ProhibitedRule) => !relevantDefinitions.has(rule.target)
    );

  // Spread out the "prohibited" rules into multiple rules.
  return [
    {
      ...rule,
      rules: [
        ...conditionalNonProhibitedRules,
        ...conditionalProhibitedRulesNotToExpand,
        ...newConditionalProhibitedRules,
      ],
      rulesConnector: 'and',
    },
  ];
}

/**
 * Expand conditions of type "present" to include all subfields. If there is a sibling to the condition connected with "and", extract that into its own rule.
 * @param rule
 * @param definitions
 * @returns
 */
function expandPresentConditions(
  rule: Rule,
  definitions: Record<string, unknown>
): Rule[] {
  if (rule.type !== 'condition') {
    return [rule];
  }

  const conditions: NestedCondition[] = rule.conditions;

  const relevantDefinitions: Map<string, string> = new Map();

  for (const condition of conditions) {
    if (isCondition(condition)) {
      if (condition.type !== 'present' || condition.value !== true) {
        continue;
      }
      const target = condition.field;
      const targetRef = getTargetRef(target, definitions);
      if (!targetRef) {
        continue;
      }
      const nestedDefinition = getDefinition(targetRef, definitions);
      if (!nestedDefinition) {
        continue;
      }
      relevantDefinitions.set(target, targetRef);
    } else {
      // Nested conditions cannot be expanded as of now.
      for (const subCondition of condition.conditions) {
        if (subCondition.type !== 'present' || subCondition.value !== true) {
          continue;
        }
        const target = subCondition.field;
        const targetRef = getTargetRef(target, definitions);
        if (!targetRef) {
          continue;
        }
        const nestedDefinition = getDefinition(targetRef, definitions);
        if (nestedDefinition) {
          throw new Error(
            `Found a nested condition in rule ${rule.id} that requires expansion. Expansion is not supported for nested conditions as of now.`
          );
        }
      }
    }
  }

  if (relevantDefinitions.size === 0) {
    return [rule];
  }

  if (
    (typeof rule.conditionsConnector === 'undefined' ||
      rule.conditionsConnector === 'and') &&
    relevantDefinitions.size > 1
  ) {
    throw new Error(
      `Multiple conditions connected with "and" found in rule ${rule.id} that need to be expanded. This is not supported as of now.`
    );
  }

  const expandedConditions: NestedCondition[] = [];

  for (const [field, targetRef] of relevantDefinitions.entries()) {
    const presentConditionToExpand = conditions.find(
      (condition) => isCondition(condition) && condition.field === field
    );

    if (!presentConditionToExpand || !isCondition(presentConditionToExpand)) {
      throw new Error(
        `No "present" condition found for field "${field}" in rule ${rule.id}.`
      );
    }

    const allNestedFieldNames = getNestedFieldNames(
      definitions,
      targetRef,
      field
    );

    expandedConditions.push(
      ...allNestedFieldNames.map(
        (fieldName): NestedCondition => ({
          type: 'present',
          value: true,
          field: fieldName,
        })
      )
    );
  }

  const conditionsNotToExpand = conditions.filter(
    (condition) =>
      !isCondition(condition) || !relevantDefinitions.has(condition.field)
  );

  if (rule.conditionsConnector === 'or' || conditions.length === 1) {
    // Simple case: We just spread out the "present" condition into multiple conditions.
    return [
      {
        ...rule,
        conditions: [...expandedConditions, ...conditionsNotToExpand],
        conditionsConnector: 'or',
      },
    ];
  } else {
    // There are multiple conditions connected with "and". Create a new rule containing the conditions not to expand.
    const newConditionalRule: ConditionalRule = {
      ...rule,
      conditions: conditionsNotToExpand,
      conditionsConnector: 'and',
    };

    return [
      newConditionalRule,
      {
        ...rule,
        conditions: expandedConditions,
        conditionsConnector: 'or',
      },
    ];
  }
}

export function transformRules(
  rules: Rule[],
  jsonSchema: Record<string, unknown>
): Rule[] {
  if (!isValidObject(jsonSchema) || !('definitions' in jsonSchema)) {
    throw new Error('Invalid JSON schema format.');
  }

  const { definitions } = jsonSchema;

  if (!isValidObject(definitions)) {
    throw new Error('Invalid definitions in JSON schema.');
  }

  const allOriginalRules = rules.reduce<Rule[]>((acc, rule) => {
    acc.push(...getRulesForBaseKeys(rule));
    return acc;
  }, []);

  const rulesWithExpandedRequiredTargets = allOriginalRules.reduce<Rule[]>(
    (acc, rule) => {
      const expandedRules = expandRequiredTargets(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    },
    []
  );

  const rulesWithExpandedProhibitedTargets =
    rulesWithExpandedRequiredTargets.reduce<Rule[]>((acc, rule) => {
      const expandedRules = expandProhibitedTargets(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  const rulesWithExpandedPresentConditions =
    rulesWithExpandedProhibitedTargets.reduce<Rule[]>((acc, rule) => {
      const expandedRules = expandPresentConditions(rule, definitions);
      acc.push(...expandedRules);
      return acc;
    }, []);

  return rulesWithExpandedPresentConditions;
}
