import { isCondition, Rule } from '../../projects/iso20022-lib/rules';

export function computeAffectedFieldsForFieldValueChange(
  rules: Rule[]
): Map<string, Set<string>> {
  const affectedFieldsForChange: Map<string, Set<string>> = new Map();

  for (const rule of rules) {
    if (rule.type === 'condition') {
      // For conditional rules, if the changed field is part of a condition, all targets of subrules might be affected
      const conditionFields: string[] = rule.conditions.flatMap((condition) =>
        isCondition(condition)
          ? [condition.field]
          : condition.conditions.map((c) => c.field)
      );

      const subRuleTargets: string[] = rule.rules.map(
        (subRule) => subRule.target
      );

      for (const field of conditionFields) {
        if (affectedFieldsForChange.has(field)) {
          const affectedFieldsForRule = affectedFieldsForChange.get(field);
          for (const subRuleTarget of subRuleTargets) {
            affectedFieldsForRule?.add(subRuleTarget);
          }
        } else {
          affectedFieldsForChange.set(field, new Set(subRuleTargets));
        }
      }

      // Also, if the 'rulesConnector' is 'or', that means that changing a subrule target's field, that might affect all the other subrule target fields.
      if (rule.rulesConnector === 'or') {
        for (const subRuleTarget of subRuleTargets) {
          if (affectedFieldsForChange.has(subRuleTarget)) {
            const affectedFieldsForRule =
              affectedFieldsForChange.get(subRuleTarget);
            for (const otherSubRuleTarget of subRuleTargets) {
              if (otherSubRuleTarget !== subRuleTarget) {
                affectedFieldsForRule?.add(otherSubRuleTarget);
              }
            }
          } else {
            affectedFieldsForChange.set(
              subRuleTarget,
              new Set(subRuleTargets.filter((t) => t !== subRuleTarget))
            );
          }
        }
      }
    } else {
      if (affectedFieldsForChange.has(rule.target)) {
        const affectedFieldsForRule = affectedFieldsForChange.get(rule.target);
        affectedFieldsForRule?.add(rule.target);
      } else {
        affectedFieldsForChange.set(rule.target, new Set([rule.target]));
      }
    }

    if (rule.type === 'contains') {
      // For 'contains' rules, the target of the rule is also affected by changes to the 'otherField's of the rule.
      const otherFields = rule.otherFields;
      for (const otherField of otherFields) {
        if (affectedFieldsForChange.has(otherField)) {
          const affectedFieldsForRule = affectedFieldsForChange.get(otherField);
          affectedFieldsForRule?.add(rule.target);
        } else {
          affectedFieldsForChange.set(otherField, new Set([rule.target]));
        }
      }
    }
  }

  return affectedFieldsForChange;
}

export function mapToObject(
  map: Map<string, Set<string>>
): Record<string, string[]> {
  const obj: Record<string, string[]> = {};
  for (const [key, valueSet] of map.entries()) {
    obj[key] = Array.from(valueSet);
  }

  return obj;
}
