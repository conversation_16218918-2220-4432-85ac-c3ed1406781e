/**
 * Converts a string to snake case.
 * @param str the string to convert to snake case.
 * @returns the snake case string.
 * @example
 * toSnakeCase("GroupHeader") // "group_header"
 * toSnakeCase("FIToFICustomerCreditTransferV08") // "fi_to_fi_customer_credit_transfer_v08"
 * toSnakeCase("CreditTransferTransactionInformation") // "credit_transfer_transaction_information"
 * toSnakeCase("PreviousInstructingAgent1Account") // "previous_instructing_agent1_account"
 * toSnakeCase("IBAN") // "iban"
 */
export function toSnakeCase(str: string): string {
  const result: string[] = [];
  let word = '';

  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    const prev = str[i - 1];
    const next = str[i + 1];

    const isUpper = /[A-Z]/.test(char);
    const isPrevLowerAlphaNum =
      typeof prev !== 'undefined' && /[a-z0-9]/.test(prev);
    const isNextLowerAlphaNum =
      typeof next !== 'undefined' && /[a-z0-9]/.test(next);

    if (i > 0 && isUpper && (isPrevLowerAlphaNum || isNextLowerAlphaNum)) {
      result.push(word);
      word = char;
    } else {
      word += char;
    }
  }

  if (word) result.push(word);

  return result
    .filter((word) => !!word)
    .map((word) => word.toLowerCase())
    .join('_');
}

export function isValidObject(obj: unknown): obj is Record<string, unknown> {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj);
}

export function isValidStringArray(arr: unknown): arr is string[] {
  return (
    typeof arr !== 'undefined' &&
    arr !== null &&
    Array.isArray(arr) &&
    arr.every((element) => typeof element === 'string')
  );
}

export function getRef(property: Record<string, unknown>): string | undefined {
  const ref =
    property['$ref'] ||
    (isValidObject(property['items']) ? property['items']['$ref'] : undefined);
  return ref && typeof ref === 'string' ? ref.split('/').pop() : undefined;
}

export function getName(property: Record<string, unknown>): string | undefined {
  const name = property['name'];
  return name && typeof name === 'string' ? name : undefined;
}

export function getNestedFieldNames(
  definitions: Record<string, unknown>,
  ref: string,
  nestedFieldName: string
): string[] {
  const definition = definitions[ref];
  if (!isValidObject(definition)) {
    throw new Error(`Invalid JSON schema: '${ref}' is not a valid object`);
  }

  if (definition['type'] === 'string' || definition['type'] === 'boolean') {
    return [nestedFieldName];
  }

  if (definition['type'] === 'array') {
    console.log(
      `Array type on definition level is not supported. Skipping field ${nestedFieldName}.`
    );
    return [];
  }

  const nestedFieldNames: string[] = [];

  if (definition['type'] === 'object') {
    const properties = definition['properties'];
    const oneOf = definition['oneOf'];

    if (!properties && !oneOf) {
      return [];
    }

    if (isValidObject(properties)) {
      for (const [propertyKey, propertyValue] of Object.entries(properties)) {
        if (!isValidObject(propertyValue)) {
          throw new Error(
            `Invalid JSON schema: '${propertyKey}' is not a valid object`
          );
        }

        const propertyRef = getRef(propertyValue);
        if (!propertyRef) {
          continue;
        }

        nestedFieldNames.push(
          ...getNestedFieldNames(
            definitions,
            propertyRef,
            `${nestedFieldName}-${propertyKey}`
          )
        );
      }
    } else if (Array.isArray(oneOf)) {
      for (const oneOfEntry of oneOf) {
        const properties = oneOfEntry.properties;
        if (
          !isValidObject(properties) ||
          Object.keys(properties).length !== 1
        ) {
          throw new Error(
            `Invalid properties format in oneOf entry: ${oneOfEntry}`
          );
        }

        const [propertyKey, propertyValue] = Object.entries(properties)[0];

        if (!isValidObject(propertyValue)) {
          throw new Error(`Invalid property format for "${propertyKey}".`);
        }

        const propertyRef = getRef(propertyValue);
        if (!propertyRef) {
          continue;
        }

        nestedFieldNames.push(
          ...getNestedFieldNames(
            definitions,
            propertyRef,
            `${nestedFieldName}-${propertyKey}`
          )
        );
      }
    }
  }
  return nestedFieldNames;
}

function removeFromEnd(str: string, suffix: string) {
  if (!suffix || !str.endsWith(suffix)) {
    return str;
  }

  return str.slice(0, -suffix.length);
}

/**
 * Adds a base key to the before the existing key. Replaces the existing key if specified.
 * @param original Entire original key, including prefix and suffix.
 * @param baseKey The base key to add after the prefix and before the original key.
 * @param connector The connector to use between the base key and the original key.
 * @param suffix Suffix to keep even when replacing the entire original key.
 * @param replaceEntireKey If true, replaces the entire original key with the base key. If false, appends the base key before the original key.
 * @returns The new key with the base key added.
 */
export function addBaseKey(
  original: string,
  baseKey: string,
  connector: string,
  suffix = '',
  replaceEntireKey = false
): string {
  const originalWithoutSuffix = removeFromEnd(original, suffix);
  const originalSplit = originalWithoutSuffix.split(':');

  let prefix = '';
  if (originalSplit.length > 1) {
    if (originalSplit.length > 2) {
      throw new Error(
        `Invalid rule ID "${original}" - it contains more than one colon.`
      );
    }
    prefix = originalSplit[0];
  }

  const originalWithoutPrefix = originalSplit[1] || originalSplit[0];

  if (!originalWithoutPrefix) {
    throw new Error(
      `Invalid rule ID "${original}" - it does not contain a valid base key.`
    );
  }

  return `${prefix}${prefix.length > 0 ? ':' : ''}${baseKey}${
    replaceEntireKey ? '' : connector
  }${replaceEntireKey ? '' : originalWithoutPrefix}${suffix}`;
}
