#!/usr/bin/env node

import * as fs from 'fs';
import { Document, DOMParser } from '@xmldom/xmldom';
import minimist from 'minimist';
import { XsdElement } from './types';
import { extractXsdElementMappings } from './extract-xsd-names';
import { toSnakeCase } from './utils';
import { transformJsonSchema } from './transform-json-schema';
import { generateRules } from './generate-rules';
import { transformRules } from './transform-rules';
import { customRules } from '../input/pacs.008.001.08_cbprplus/custom_rules';
import { combineRules } from './combine-rules';
import { validateRules } from './validate-rules';
import { filterClientRules } from './filter-client-rules';
import {
  computeAffectedFieldsForFieldValueChange,
  mapToObject,
} from './precompute-utilities';

const INTERMEDIARY_RESULTS_FOLDER = 'rule-generation/intermediary-results';

/**
 * Parse command line arguments
 */
function parseArgs(): {
  jsonFile: string;
  xsdFile: string;
  outputFolder: string;
  baseElement?: string;
} {
  const argv = minimist(process.argv.slice(2), {
    string: ['output-folder', 'base-element'],
    default: {
      outputFolder: '',
    },
  });

  if (argv._.length < 2) {
    console.error(
      'Usage: node transform-json-schema.js <json_schema> <xsd_schema> [--output-folder /output] [--base-element element_name]'
    );
    process.exit(1);
  }

  return {
    jsonFile: argv._[0],
    xsdFile: argv._[1],
    outputFolder: argv['output-folder'],
    baseElement: argv['base-element'],
  };
}

async function main() {
  try {
    const args = parseArgs();

    // Read input files
    const jsonContent = fs.readFileSync(args.jsonFile, 'utf8');
    const xsdContent = fs.readFileSync(args.xsdFile, 'utf8');

    // Parse input files
    const jsonSchema = JSON.parse(jsonContent);
    const parser = new DOMParser();
    const xmlDoc: Document = parser.parseFromString(xsdContent, 'text/xml');

    const nameMappings: XsdElement = extractXsdElementMappings(xmlDoc);
    fs.writeFileSync(
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`,
      JSON.stringify(nameMappings, null, 2),
      'utf8'
    );
    console.log(
      'Wrote name mappings to ',
      `${INTERMEDIARY_RESULTS_FOLDER}/name-mappings.json`
    );
    const rootElementName = args.baseElement ?? nameMappings.fullName;
    if (toSnakeCase(nameMappings.fullName) !== rootElementName) {
      throw new Error(
        `Base element name "${toSnakeCase(
          nameMappings.fullName
        )}" does not match the expected name "${rootElementName}".`
      );
    }

    // Transform JSON schema
    const transformedJsonSchema = transformJsonSchema(jsonSchema, nameMappings);
    fs.writeFileSync(
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-schema.json`,
      JSON.stringify(transformedJsonSchema, null, 2),
      'utf8'
    );
    console.log(
      'Wrote transformed schema to ',
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-schema.json`
    );

    // Generate rules from transformed JSON schema
    const generatedRules = generateRules(
      transformedJsonSchema,
      nameMappings.abbrName
    );
    fs.writeFileSync(
      `${INTERMEDIARY_RESULTS_FOLDER}/generated-rules.json`,
      JSON.stringify(generatedRules, null, 2),
      'utf8'
    );
    console.log(
      'Wrote generated rules to ',
      `${INTERMEDIARY_RESULTS_FOLDER}/generated-rules.json`
    );

    // Transform custom rules
    const transformedRules = transformRules(customRules, transformedJsonSchema);
    fs.writeFileSync(
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-custom-rules.json`,
      JSON.stringify(transformedRules, null, 2),
      'utf8'
    );
    console.log(
      'Wrote transformed custom rules to ',
      `${INTERMEDIARY_RESULTS_FOLDER}/transformed-custom-rules.json`
    );

    // Combine generated rules with custom rules
    const combinedRules = combineRules(generatedRules, transformedRules);
    validateRules(combinedRules);
    fs.writeFileSync(
      `${INTERMEDIARY_RESULTS_FOLDER}/combined-rules.json`,

      JSON.stringify(combinedRules, null, 2),
      'utf8'
    );
    console.log(
      'Wrote combined rules to ',
      `${INTERMEDIARY_RESULTS_FOLDER}/combined-rules.json`
    );

    const clientRules = filterClientRules(combinedRules);
    fs.writeFileSync(
      args.outputFolder + '/client-rules.json',
      JSON.stringify(clientRules, null, 2),
      'utf8'
    );
    console.log(
      'Wrote client rules to ',
      args.outputFolder + '/client-rules.json'
    );

    const affectedFieldsForFieldValueChange =
      computeAffectedFieldsForFieldValueChange(clientRules);
    const affectedFields = mapToObject(affectedFieldsForFieldValueChange);
    fs.writeFileSync(
      args.outputFolder + '/affected-fields-for-field-value-change.json',
      JSON.stringify(affectedFields, null, 2),
      'utf8'
    );
    console.log(
      'Wrote affected fields for field value change to ',
      args.outputFolder + '/affected-fields-for-field-value-change.json'
    );
  } catch (error: unknown) {
    console.error(error);
    process.exit(1);
  }
}

main();
