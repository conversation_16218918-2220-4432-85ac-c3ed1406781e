[{"id": "FIToFICstmrCdtTrf-GrpHdr-MsgId-serverOnly", "description": "MessageIdentification is serverOnly", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-MsgId"}, {"id": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs-serverOnly", "description": "NumberOfTransactions is serverOnly", "type": "serverOnly", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs"}, {"id": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs-value", "description": "NumberOfTransactions must be exactly 1", "type": "value", "value": "1", "isEqual": true, "target": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs"}, {"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-conditional-required", "description": "If at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' is present, then at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr' must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}], "rules": [{"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm-conditional-required", "description": "If at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr' is present, then at least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R12:FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element 'FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm' must be present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R13:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-conditional-value", "description": "The code 'HOLD' is not allowed if the code 'CHQB' is present.", "type": "condition", "conditions": [{"type": "value", "value": "CHQB", "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}], "rules": [{"id": "R13:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-value-conditional", "description": "The code 'HOLD' is not allowed.", "type": "value", "value": "HOLD", "isEqual": false, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}]}, {"id": "R14:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-conditional-value", "description": "The code 'TELB' is not allowed if the code 'PHOB' is present.", "type": "condition", "conditions": [{"type": "value", "value": "PHOB", "field": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}], "rules": [{"id": "R14:FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd-value-conditional", "description": "The code 'TELB' is not allowed.", "type": "value", "value": "TELB", "isEqual": false, "target": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd"}]}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId_Nm_PstlAddr-conditional-prohibited", "description": "If the BICFI is present, it must not be accompanied by a Clearing Code, a Name, or an Address", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Name.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId_Nm_PstlAddr-conditional-prohibited", "description": "If the BICFI is present, it must not be accompanied by a Clearing Code, a Name, or an Address", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Name.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId_Nm_PstlAddr-conditional-prohibited", "description": "If the BICFI is present, it must not be accompanied by a Clearing Code, a Name, or an Address", "type": "condition", "conditions": [{"field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "type": "present", "value": true}], "rules": [{"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Name.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "The BICFI must not be accompanied by a Clearing Code.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R16:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "The BICFI must not be accompanied by a Postal Address.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI-conditional-prohibited", "description": "If the Clearing Code or the Name or the Address is present, the BICFI must not be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "The BICFI must not be present.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI-conditional-prohibited", "description": "If the Clearing Code or the Name or the Address is present, the BICFI must not be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "The BICFI must not be present.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI-conditional-prohibited", "description": "If the Clearing Code or the Name or the Address is present, the BICFI must not be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or", "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "The BICFI must not be present.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}]}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If the Name is present, either the unstructed postal address or town name and country are required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If the Name is present, either the unstructed postal address or town name and country are required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If the Name is present, either the unstructed postal address or town name and country are required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If the Postal Address is present, the name is required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "The Name is required.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If the Postal Address is present, the name is required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "The Name is required.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If the Postal Address is present, the name is required.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R17-R18:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "The Name is required.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If at least one occurrence of the element Name is present, then at least one occurrence of the element Postal Address must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If at least one occurrence of the element Name is present, then at least one occurrence of the element Postal Address must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-conditional-required", "description": "If TwnNm is present, then <PERSON><PERSON> must also be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Required if TwnNm is present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rulesConnector": "and"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-conditional-required", "description": "If at least one occurrence of the element Name is present, then at least one occurrence of the element Postal Address must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-required-conditional", "description": "At least one occurrence of the element AdrLine must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "At least one occurrence of the element TwnNm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}], "rulesConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If at least one occurrence of the element Postal Address is present, then at least one occurrence of the element Name must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element Name must be present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If at least one occurrence of the element Postal Address is present, then at least one occurrence of the element Name must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element Name must be present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-conditional-required", "description": "If at least one occurrence of the element Postal Address is present, then at least one occurrence of the element Name must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "R20-R25-R29:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element Name must be present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}], "conditionsConnector": "or"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm_Ctry-conditional-required", "description": "If the Address Line is present, and either Department or SubDepartment or StreetName or BuildingNumber or BuildingName or Floor or PostBox or Room or PostCode or TownLocationName or DistrictName or CountrySubDivision are present, then TownName and Country are mandatory and a maximum of two occurrences of Address Line are allowed.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "TownName is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Country is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxItems-conditional", "description": "A maximum of two occurrences of Address Line are allowed.", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm_Ctry-conditional-required", "description": "If the Address Line is present, and either Department or SubDepartment or StreetName or BuildingNumber or BuildingName or Floor or PostBox or Room or PostCode or TownLocationName or DistrictName or CountrySubDivision are present, then TownName and Country are mandatory and a maximum of two occurrences of Address Line are allowed.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "TownName is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Country is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxItems-conditional", "description": "A maximum of two occurrences of Address Line are allowed.", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm_Ctry-conditional-required", "description": "If the Address Line is present, and either Department or SubDepartment or StreetName or BuildingNumber or BuildingName or Floor or PostBox or Room or PostCode or TownLocationName or DistrictName or CountrySubDivision are present, then TownName and Country are mandatory and a maximum of two occurrences of Address Line are allowed.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}], "conditionsConnector": "or"}], "conditionsConnector": "and", "rules": [{"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-required-conditional", "description": "TownName is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-required-conditional", "description": "Country is mandatory if the Address Line is present and any of the other fields are present.", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "R22-R27-R31:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxItems-conditional", "description": "A maximum of two occurrences of Address Line are allowed.", "type": "maxItems", "value": 2, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R23:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-contains", "description": "Data present in structured elements within the Postal Address must not be repeated in AddressLine.", "type": "contains", "contains": false, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "otherFields": ["FinInstnId-PstlAdr-Dept", "FinInstnId-PstlAdr-SubDept", "FinInstnId-PstlAdr-StrtNm", "FinInstnId-PstlAdr-BldgNb", "FinInstnId-PstlAdr-BldgNm", "FinInstnId-PstlAdr-Flr", "FinInstnId-PstlAdr-PstBx", "FinInstnId-PstlAdr-Room", "FinInstnId-PstlAdr-PstCd", "FinInstnId-PstlAdr-TwnNm", "FinInstnId-PstlAdr-TwnLctnNm", "FinInstnId-PstlAdr-DstrctNm", "FinInstnId-PstlAdr-CtrySubDvsn", "FinInstnId-PstlAdr-Ctry"]}, {"id": "R23:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-contains", "description": "Data present in structured elements within the Postal Address must not be repeated in AddressLine.", "type": "contains", "contains": false, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "otherFields": ["FinInstnId-PstlAdr-Dept", "FinInstnId-PstlAdr-SubDept", "FinInstnId-PstlAdr-StrtNm", "FinInstnId-PstlAdr-BldgNb", "FinInstnId-PstlAdr-BldgNm", "FinInstnId-PstlAdr-Flr", "FinInstnId-PstlAdr-PstBx", "FinInstnId-PstlAdr-Room", "FinInstnId-PstlAdr-PstCd", "FinInstnId-PstlAdr-TwnNm", "FinInstnId-PstlAdr-TwnLctnNm", "FinInstnId-PstlAdr-DstrctNm", "FinInstnId-PstlAdr-CtrySubDvsn", "FinInstnId-PstlAdr-Ctry"]}, {"id": "R23:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-contains", "description": "Data present in structured elements within the Postal Address must not be repeated in AddressLine.", "type": "contains", "contains": false, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "otherFields": ["FinInstnId-PstlAdr-Dept", "FinInstnId-PstlAdr-SubDept", "FinInstnId-PstlAdr-StrtNm", "FinInstnId-PstlAdr-BldgNb", "FinInstnId-PstlAdr-BldgNm", "FinInstnId-PstlAdr-Flr", "FinInstnId-PstlAdr-PstBx", "FinInstnId-PstlAdr-Room", "FinInstnId-PstlAdr-PstCd", "FinInstnId-PstlAdr-TwnNm", "FinInstnId-PstlAdr-TwnLctnNm", "FinInstnId-PstlAdr-DstrctNm", "FinInstnId-PstlAdr-CtrySubDvsn", "FinInstnId-PstlAdr-Ctry"]}, {"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-maxLength", "description": "If Postal Address is present and if no other element than Address Line is present then every occurrence of Address Line must not exceed 35 characters.", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxLength-conditional", "description": "AddressLine must be at most 35 characters long.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-maxLength", "description": "If Postal Address is present and if no other element than Address Line is present then every occurrence of Address Line must not exceed 35 characters.", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxLength-conditional", "description": "AddressLine must be at most 35 characters long.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-conditional-maxLength", "description": "If Postal Address is present and if no other element than Address Line is present then every occurrence of Address Line must not exceed 35 characters.", "type": "condition", "conditions": [{"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": false, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}], "rules": [{"id": "R24-R28-R32:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-maxLength-conditional", "description": "AddressLine must be at most 35 characters long.", "type": "max<PERSON><PERSON><PERSON>", "value": 35, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}]}, {"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-conditional-required", "description": "If ThirdReimbursementAgent is present, then InstructingReimbursementAgent must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI-required-conditional", "description": "At least one occurrence of the element FinInstnId-BICFI must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element FinInstnId-Nm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or", "conditionsConnector": "or"}, {"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-conditional-required", "description": "If ThirdReimbursementAgent is present, then InstructedReimbursementAgent must be present.", "type": "condition", "conditions": [{"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"type": "present", "value": true, "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rules": [{"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI-required-conditional", "description": "At least one occurrence of the element FinInstnId-BICFI must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "ThirdReimbursementAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element FinInstnId-Nm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or", "conditionsConnector": "or"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt_InstdRmbrsmntAgt_ThrdRmbrsmntAgt-conditional-prohibited", "description": "If SettlementMethod is equal to INDA or INGA, then ReimbursementAgent(s) and ClearingSystem are not allowed.", "type": "condition", "conditions": [{"type": "value", "value": "INDA", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}, {"type": "value", "value": "INGA", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "conditionsConnector": "or", "rules": [{"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "InstructingReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "InstructedReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry"}, {"id": "SettlementMethodAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine-prohibited-conditional", "description": "ThirdReimbursementAgent is not allowed if SettlementMethod is INDA or INGA.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine"}], "rulesConnector": "and"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-conditional-prohibited", "description": "If SettlementMethod is equal to COVE, then SettlementAccount and ClearingSystem are not allowed.", "type": "condition", "conditions": [{"type": "value", "value": "COVE", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "rules": [{"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry"}, {"id": "SettlementMethodCoverRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id-prohibited-conditional", "description": "SettlementAccount is not allowed if SettlementMethod is COVE.", "type": "prohibited", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id"}], "rulesConnector": "and"}, {"id": "SettlementMethodCoverAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt_InstgRmbrsmntAgt-conditional-required", "description": "If SettlementMethod is equal to COVE, then InstructedReimbursementAgent or InstructingReimbursementAgent must be present.", "type": "condition", "conditions": [{"type": "value", "value": "COVE", "field": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd"}], "rules": [{"id": "SettlementMethodCoverAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI-required-conditional", "description": "At least one occurrence of the element FinInstnId-BICFI must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodCoverAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element FinInstnId-Nm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm"}, {"id": "SettlementMethodCoverAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI-required-conditional", "description": "At least one occurrence of the element FinInstnId-BICFI must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI"}, {"id": "SettlementMethodCoverAgentRule:FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm-required-conditional", "description": "At least one occurrence of the element FinInstnId-Nm must be present", "type": "required", "value": true, "target": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm"}], "rulesConnector": "or"}]