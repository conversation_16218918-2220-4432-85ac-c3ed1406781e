{"fullName": "FIToFICustomerCreditTransferV08", "abbrName": "FIToFICstmrCdtTrf", "nestedAbbrName": "FIToFICstmrCdtTrf", "children": [{"fullName": "GroupHeader", "abbrName": "GrpHdr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr", "children": [{"fullName": "MessageIdentification", "abbrName": "MsgId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-MsgId", "children": []}, {"fullName": "CreationDateTime", "abbrName": "CreDtTm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-CreDtTm", "children": []}, {"fullName": "NumberOfTransactions", "abbrName": "NbOfTxs", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-NbOfTxs", "children": []}, {"fullName": "SettlementInformation", "abbrName": "SttlmInf", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf", "children": [{"fullName": "SettlementMethod", "abbrName": "SttlmMtd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmMtd", "children": []}, {"fullName": "SettlementAccount", "abbrName": "SttlmAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-SttlmAcct-Prxy-Id", "children": []}]}]}, {"fullName": "InstructingReimbursementAgent", "abbrName": "InstgRmbrsmntAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "InstructingReimbursementAgentAccount", "abbrName": "InstgRmbrsmntAgtAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgtAcct-Prxy-Id", "children": []}]}]}, {"fullName": "InstructedReimbursementAgent", "abbrName": "InstdRmbrsmntAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "InstructedReimbursementAgentAccount", "abbrName": "InstdRmbrsmntAgtAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstdRmbrsmntAgtAcct-Prxy-Id", "children": []}]}]}, {"fullName": "ThirdReimbursementAgent", "abbrName": "ThrdRmbrsmntAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "ThirdReimbursementAgentAccount", "abbrName": "ThrdRmbrsmntAgtAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-GrpHdr-SttlmInf-ThrdRmbrsmntAgtAcct-Prxy-Id", "children": []}]}]}]}]}, {"fullName": "CreditTransferTransactionInformation", "abbrName": "CdtTrfTxInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf", "children": [{"fullName": "PaymentIdentification", "abbrName": "PmtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId", "children": [{"fullName": "InstructionIdentification", "abbrName": "InstrId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-InstrId", "children": []}, {"fullName": "EndToEndIdentification", "abbrName": "EndToEndId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-EndToEndId", "children": []}, {"fullName": "TransactionIdentification", "abbrName": "TxId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-TxId", "children": []}, {"fullName": "UETR", "abbrName": "UETR", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-UETR", "children": []}, {"fullName": "ClearingSystemReference", "abbrName": "ClrSysRef", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtId-ClrSysRef", "children": []}]}, {"fullName": "PaymentTypeInformation", "abbrName": "PmtTpInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf", "children": [{"fullName": "InstructionPriority", "abbrName": "InstrPrty", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-InstrPrty", "children": []}, {"fullName": "ClearingChannel", "abbrName": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-ClrChanl", "children": []}, {"fullName": "ServiceLevel", "abbrName": "SvcLvl", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-SvcLvl-Prtry", "children": []}]}, {"fullName": "LocalInstrument", "abbrName": "LclInstrm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-LclInstrm-Prtry", "children": []}]}, {"fullName": "CategoryPurpose", "abbrName": "CtgyPurp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PmtTpInf-CtgyPurp-Prtry", "children": []}]}]}, {"fullName": "InterbankSettlementAmount", "abbrName": "IntrBkSttlmAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmAmt-Ccy", "children": []}]}, {"fullName": "InterbankSettlementDate", "abbrName": "IntrBkSttlmDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrBkSttlmDt", "children": []}, {"fullName": "SettlementPriority", "abbrName": "SttlmPrty", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmPrty", "children": []}, {"fullName": "SettlementTimeIndication", "abbrName": "SttlmTmIndctn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn", "children": [{"fullName": "DebitDateTime", "abbrName": "DbtDtTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-DbtDtTm", "children": []}, {"fullName": "CreditDateTime", "abbrName": "CdtDtTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmIndctn-CdtDtTm", "children": []}]}, {"fullName": "SettlementTimeRequest", "abbrName": "SttlmTmReq", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq", "children": [{"fullName": "CLSTime", "abbrName": "CLSTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-CLSTm", "children": []}, {"fullName": "TillTime", "abbrName": "TillTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-TillTm", "children": []}, {"fullName": "FromTime", "abbrName": "FrTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-FrTm", "children": []}, {"fullName": "RejectTime", "abbrName": "RjctTm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-SttlmTmReq-RjctTm", "children": []}]}, {"fullName": "InstructedAmount", "abbrName": "InstdAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAmt-Ccy", "children": []}]}, {"fullName": "ExchangeRate", "abbrName": "XchgRate", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-XchgRate", "children": []}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "ChrgBr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgBr", "children": []}, {"fullName": "ChargesInformation", "abbrName": "ChrgsInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf", "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Amt-Ccy", "children": []}]}, {"fullName": "Agent", "abbrName": "Agt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-ChrgsInf-Agt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}]}, {"fullName": "PreviousInstructingAgent1", "abbrName": "PrvsInstgAgt1", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent1Account", "abbrName": "PrvsInstgAgt1Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt1Acct-Prxy-Id", "children": []}]}]}, {"fullName": "PreviousInstructingAgent2", "abbrName": "PrvsInstgAgt2", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent2Account", "abbrName": "PrvsInstgAgt2Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt2Acct-Prxy-Id", "children": []}]}]}, {"fullName": "PreviousInstructingAgent3", "abbrName": "PrvsInstgAgt3", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "PreviousInstructingAgent3Account", "abbrName": "PrvsInstgAgt3Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-PrvsInstgAgt3Acct-Prxy-Id", "children": []}]}]}, {"fullName": "InstructingAgent", "abbrName": "InstgAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstgAgt-FinInstnId-LEI", "children": []}]}]}, {"fullName": "InstructedAgent", "abbrName": "InstdAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstdAgt-FinInstnId-LEI", "children": []}]}]}, {"fullName": "IntermediaryAgent1", "abbrName": "IntrmyAgt1", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "IntermediaryAgent1Account", "abbrName": "IntrmyAgt1Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt1Acct-Prxy-Id", "children": []}]}]}, {"fullName": "IntermediaryAgent2", "abbrName": "IntrmyAgt2", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "IntermediaryAgent2Account", "abbrName": "IntrmyAgt2Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt2Acct-Prxy-Id", "children": []}]}]}, {"fullName": "IntermediaryAgent3", "abbrName": "IntrmyAgt3", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "IntermediaryAgent3Account", "abbrName": "IntrmyAgt3Acct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-IntrmyAgt3Acct-Prxy-Id", "children": []}]}]}, {"fullName": "UltimateDebtor", "abbrName": "UltmtDbtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtDbtr-CtryOfRes", "children": []}]}, {"fullName": "Initiating<PERSON><PERSON><PERSON>", "abbrName": "InitgPty", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InitgPty-CtryOfRes", "children": []}]}, {"fullName": "Debtor", "abbrName": "Dbtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Dbtr-CtryOfRes", "children": []}]}, {"fullName": "DebtorAccount", "abbrName": "DbtrAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAcct-Prxy-Id", "children": []}]}]}, {"fullName": "DebtorAgent", "abbrName": "DbtrAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}]}, {"fullName": "DebtorAgentAccount", "abbrName": "DbtrAgtAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-DbtrAgtAcct-Prxy-Id", "children": []}]}]}, {"fullName": "CreditorAgent", "abbrName": "CdtrAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt", "children": [{"fullName": "FinancialInstitutionIdentification", "abbrName": "FinInstnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId", "children": [{"fullName": "BICFI", "abbrName": "BICFI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-BICFI", "children": []}, {"fullName": "ClearingSystemMemberIdentification", "abbrName": "ClrSysMmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId", "children": [{"fullName": "ClearingSystemIdentification", "abbrName": "ClrSysId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-ClrSysId-Cd", "children": []}]}, {"fullName": "MemberIdentification", "abbrName": "MmbId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-ClrSysMmbId-MmbId", "children": []}]}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-LEI", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-FinInstnId-PstlAdr-AdrLine", "children": []}]}]}, {"fullName": "BranchIdentification", "abbrName": "BrnchId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgt-BrnchId-Id", "children": []}]}]}, {"fullName": "CreditorAgentAccount", "abbrName": "CdtrAgtAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAgtAcct-Prxy-Id", "children": []}]}]}, {"fullName": "Creditor", "abbrName": "Cdtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Cdtr-CtryOfRes", "children": []}]}, {"fullName": "CreditorAccount", "abbrName": "CdtrAcct", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id", "children": [{"fullName": "IBAN", "abbrName": "IBAN", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-IBAN", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Id-Othr-Issr", "children": []}]}]}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Tp-Prtry", "children": []}]}, {"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Ccy", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Nm", "children": []}, {"fullName": "Proxy", "abbrName": "Prxy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Tp-Prtry", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-CdtrAcct-Prxy-Id", "children": []}]}]}, {"fullName": "UltimateCreditor", "abbrName": "UltmtCdtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-UltmtCdtr-CtryOfRes", "children": []}]}, {"fullName": "InstructionForCreditorAgent", "abbrName": "InstrForCdtrAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-Cd", "children": []}, {"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForCdtrAgt-InstrInf", "children": []}]}, {"fullName": "InstructionForNextAgent", "abbrName": "InstrForNxtAgt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt", "children": [{"fullName": "InstructionInformation", "abbrName": "InstrInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-InstrForNxtAgt-InstrInf", "children": []}]}, {"fullName": "Purpose", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Purp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-Purp-Prtry", "children": []}]}, {"fullName": "RegulatoryReporting", "abbrName": "RgltryRptg", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg", "children": [{"fullName": "DebitCreditReportingIndicator", "abbrName": "DbtCdtRptgInd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-DbtCdtRptgInd", "children": []}, {"fullName": "Authority", "abbrName": "<PERSON><PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Nm", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Authrty-Ctry", "children": []}]}, {"fullName": "Details", "abbrName": "Dtls", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Tp", "children": []}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Dt", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Ctry", "children": []}, {"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Cd", "children": []}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Amt-Ccy", "children": []}]}, {"fullName": "Information", "abbrName": "Inf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RgltryRptg-Dtls-Inf", "children": []}]}]}, {"fullName": "RelatedRemittanceInformation", "abbrName": "RltdRmtInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf", "children": [{"fullName": "RemittanceIdentification", "abbrName": "RmtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtId", "children": []}, {"fullName": "RemittanceLocationDetails", "abbrName": "RmtLctnDtls", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls", "children": [{"fullName": "Method", "abbrName": "Mtd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-Mtd", "children": []}, {"fullName": "ElectronicAddress", "abbrName": "ElctrncAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-ElctrncAdr", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Nm", "children": []}, {"fullName": "Address", "abbrName": "<PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RltdRmtInf-RmtLctnDtls-PstlAdr-Adr-AdrLine", "children": []}]}]}]}]}, {"fullName": "RemittanceInformation", "abbrName": "RmtInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf", "children": [{"fullName": "Unstructured", "abbrName": "Ustrd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Ustrd", "children": []}, {"fullName": "Structured", "abbrName": "Strd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd", "children": [{"fullName": "ReferredDocumentInformation", "abbrName": "RfrdDocInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp", "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-CdOrPrtry-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Tp-Issr", "children": []}]}, {"fullName": "Number", "abbrName": "Nb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-Nb", "children": []}, {"fullName": "RelatedDate", "abbrName": "RltdDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-RltdDt", "children": []}, {"fullName": "LineDetails", "abbrName": "LineDtls", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp", "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-CdOrPrtry-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Tp-Issr", "children": []}]}, {"fullName": "Number", "abbrName": "Nb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-Nb", "children": []}, {"fullName": "RelatedDate", "abbrName": "RltdDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Id-RltdDt", "children": []}]}, {"fullName": "Description", "abbrName": "Desc", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Desc", "children": []}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt", "children": [{"fullName": "DuePayableAmount", "abbrName": "DuePyblAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DuePyblAmt-Ccy", "children": []}]}, {"fullName": "DiscountAppliedAmount", "abbrName": "DscntApldAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Tp-Prtry", "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-DscntApldAmt-Amt-Ccy", "children": []}]}]}, {"fullName": "CreditNoteAmount", "abbrName": "CdtNoteAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-CdtNoteAmt-Ccy", "children": []}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Tp-Prtry", "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-TaxAmt-Amt-Ccy", "children": []}]}]}, {"fullName": "AdjustmentAmountAndReason", "abbrName": "AdjstmntAmtAndRsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn", "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Amt-Ccy", "children": []}]}, {"fullName": "CreditDebitIndicator", "abbrName": "CdtDbtInd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-CdtDbtInd", "children": []}, {"fullName": "Reason", "abbrName": "Rsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-Rsn", "children": []}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-AdjstmntAmtAndRsn-AddtlInf", "children": []}]}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocInf-LineDtls-Amt-RmtdAmt-Ccy", "children": []}]}]}]}]}, {"fullName": "ReferredDocumentAmount", "abbrName": "RfrdDocAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt", "children": [{"fullName": "DuePayableAmount", "abbrName": "DuePyblAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DuePyblAmt-Ccy", "children": []}]}, {"fullName": "DiscountAppliedAmount", "abbrName": "DscntApldAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Tp-Prtry", "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-DscntApldAmt-Amt-Ccy", "children": []}]}]}, {"fullName": "CreditNoteAmount", "abbrName": "CdtNoteAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-CdtNoteAmt-Ccy", "children": []}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Tp-Prtry", "children": []}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-TaxAmt-Amt-Ccy", "children": []}]}]}, {"fullName": "AdjustmentAmountAndReason", "abbrName": "AdjstmntAmtAndRsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn", "children": [{"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Amt-Ccy", "children": []}]}, {"fullName": "CreditDebitIndicator", "abbrName": "CdtDbtInd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-CdtDbtInd", "children": []}, {"fullName": "Reason", "abbrName": "Rsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-Rsn", "children": []}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-AdjstmntAmtAndRsn-AddtlInf", "children": []}]}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-RfrdDocAmt-RmtdAmt-Ccy", "children": []}]}]}, {"fullName": "CreditorReferenceInformation", "abbrName": "CdtrRefInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp", "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-CdOrPrtry-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Tp-Issr", "children": []}]}, {"fullName": "Reference", "abbrName": "Ref", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-CdtrRefInf-Ref", "children": []}]}, {"fullName": "Invoicer", "abbrName": "Invcr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcr-CtryOfRes", "children": []}]}, {"fullName": "Invoicee", "abbrName": "Invcee", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-Invcee-CtryOfRes", "children": []}]}, {"fullName": "TaxRemittance", "abbrName": "TaxRmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt", "children": [{"fullName": "Creditor", "abbrName": "Cdtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr", "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxId", "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-RegnId", "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Cdtr-TaxTp", "children": []}]}, {"fullName": "Debtor", "abbrName": "Dbtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr", "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxId", "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-RegnId", "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-TaxTp", "children": []}, {"fullName": "Authorisation", "abbrName": "Authstn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn", "children": [{"fullName": "Title", "abbrName": "Titl", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Titl", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dbtr-Authstn-Nm", "children": []}]}]}, {"fullName": "UltimateDebtor", "abbrName": "UltmtDbtr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr", "children": [{"fullName": "TaxIdentification", "abbrName": "TaxId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxId", "children": []}, {"fullName": "RegistrationIdentification", "abbrName": "RegnId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-RegnId", "children": []}, {"fullName": "TaxType", "abbrName": "TaxTp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-TaxTp", "children": []}, {"fullName": "Authorisation", "abbrName": "Authstn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn", "children": [{"fullName": "Title", "abbrName": "Titl", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Titl", "children": []}, {"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-UltmtDbtr-Authstn-Nm", "children": []}]}]}, {"fullName": "AdministrationZone", "abbrName": "AdmstnZone", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-AdmstnZone", "children": []}, {"fullName": "ReferenceNumber", "abbrName": "RefNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-RefNb", "children": []}, {"fullName": "Method", "abbrName": "Mtd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Mtd", "children": []}, {"fullName": "TotalTaxableBaseAmount", "abbrName": "TtlTaxblBaseAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxblBaseAmt-Ccy", "children": []}]}, {"fullName": "TotalTaxAmount", "abbrName": "TtlTaxAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-TtlTaxAmt-Ccy", "children": []}]}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Dt", "children": []}, {"fullName": "SequenceNumber", "abbrName": "SeqNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-SeqNb", "children": []}, {"fullName": "Record", "abbrName": "Rcrd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Tp", "children": []}, {"fullName": "Category", "abbrName": "Ctgy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Ctgy", "children": []}, {"fullName": "CategoryDetails", "abbrName": "CtgyDtls", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CtgyDtls", "children": []}, {"fullName": "DebtorS<PERSON>us", "abbrName": "DbtrSts", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-DbtrSts", "children": []}, {"fullName": "CertificateIdentification", "abbrName": "CertId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-CertId", "children": []}, {"fullName": "FormsCode", "abbrName": "FrmsCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-FrmsCd", "children": []}, {"fullName": "Period", "abbrName": "Prd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd", "children": [{"fullName": "Year", "abbrName": "Yr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Yr", "children": []}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-Tp", "children": []}, {"fullName": "FromToDate", "abbrName": "FrToDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt", "children": [{"fullName": "FromDate", "abbrName": "FrDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-FrDt", "children": []}, {"fullName": "ToDate", "abbrName": "ToDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-Prd-FrToDt-ToDt", "children": []}]}]}, {"fullName": "TaxAmount", "abbrName": "TaxAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt", "children": [{"fullName": "Rate", "abbrName": "Rate", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Rate", "children": []}, {"fullName": "TaxableBaseAmount", "abbrName": "TaxblBaseAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TaxblBaseAmt-Ccy", "children": []}]}, {"fullName": "TotalAmount", "abbrName": "TtlAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-TtlAmt-Ccy", "children": []}]}, {"fullName": "Details", "abbrName": "Dtls", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls", "children": [{"fullName": "Period", "abbrName": "Prd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd", "children": [{"fullName": "Year", "abbrName": "Yr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Yr", "children": []}, {"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-Tp", "children": []}, {"fullName": "FromToDate", "abbrName": "FrToDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt", "children": [{"fullName": "FromDate", "abbrName": "FrDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-FrDt", "children": []}, {"fullName": "ToDate", "abbrName": "ToDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Prd-FrToDt-ToDt", "children": []}]}]}, {"fullName": "Amount", "abbrName": "Amt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-TaxAmt-Dtls-Amt-Ccy", "children": []}]}]}]}, {"fullName": "AdditionalInformation", "abbrName": "AddtlInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-TaxRmt-Rcrd-AddtlInf", "children": []}]}]}, {"fullName": "GarnishmentRemittance", "abbrName": "GrnshmtRmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt", "children": [{"fullName": "Type", "abbrName": "Tp", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp", "children": [{"fullName": "CodeOrProprietary", "abbrName": "CdOrPrtry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-CdOrPrtry-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Tp-Issr", "children": []}]}, {"fullName": "Garnishee", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Grnshee-CtryOfRes", "children": []}]}, {"fullName": "GarnishmentAdministrator", "abbrName": "GrnshmtAdmstr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr", "children": [{"fullName": "Name", "abbrName": "Nm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Nm", "children": []}, {"fullName": "PostalAddress", "abbrName": "PstlAdr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr", "children": [{"fullName": "Department", "abbrName": "Dept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Dept", "children": []}, {"fullName": "SubDepartment", "abbrName": "SubDept", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-SubDept", "children": []}, {"fullName": "StreetName", "abbrName": "StrtNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-StrtNm", "children": []}, {"fullName": "BuildingNumber", "abbrName": "BldgNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNb", "children": []}, {"fullName": "BuildingName", "abbrName": "BldgNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-BldgNm", "children": []}, {"fullName": "Floor", "abbrName": "Flr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Flr", "children": []}, {"fullName": "PostBox", "abbrName": "PstBx", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstBx", "children": []}, {"fullName": "Room", "abbrName": "Room", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Room", "children": []}, {"fullName": "PostCode", "abbrName": "PstCd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-PstCd", "children": []}, {"fullName": "TownName", "abbrName": "TwnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnNm", "children": []}, {"fullName": "TownLocationName", "abbrName": "TwnLctnNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-TwnLctnNm", "children": []}, {"fullName": "DistrictName", "abbrName": "DstrctNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-DstrctNm", "children": []}, {"fullName": "CountrySubDivision", "abbrName": "CtrySubDvsn", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-CtrySubDvsn", "children": []}, {"fullName": "Country", "abbrName": "Ctry", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-Ctry", "children": []}, {"fullName": "AddressLine", "abbrName": "AdrLine", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-PstlAdr-AdrLine", "children": []}]}, {"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id", "children": [{"fullName": "OrganisationIdentification", "abbrName": "OrgId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId", "children": [{"fullName": "AnyBIC", "abbrName": "AnyBIC", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-AnyBIC", "children": []}, {"fullName": "LEI", "abbrName": "LEI", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-LEI", "children": []}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-OrgId-Othr-Issr", "children": []}]}]}, {"fullName": "PrivateIdentification", "abbrName": "PrvtId", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId", "children": [{"fullName": "DateAndPlaceOfBirth", "abbrName": "DtAndPlcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth", "children": [{"fullName": "BirthDate", "abbrName": "BirthDt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-BirthDt", "children": []}, {"fullName": "ProvinceOfBirth", "abbrName": "PrvcOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-PrvcOfBirth", "children": []}, {"fullName": "CityOfBirth", "abbrName": "CityOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CityOfBirth", "children": []}, {"fullName": "CountryOfBirth", "abbrName": "CtryOfBirth", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-DtAndPlcOfBirth-CtryOfBirth", "children": []}]}, {"fullName": "Other", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr", "children": [{"fullName": "Identification", "abbrName": "Id", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Id", "children": []}, {"fullName": "SchemeName", "abbrName": "SchmeNm", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm", "children": [{"fullName": "Code", "abbrName": "Cd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Cd", "children": []}, {"fullName": "Proprietary", "abbrName": "<PERSON><PERSON><PERSON>", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-SchmeNm-Prtry", "children": []}]}, {"fullName": "Issuer", "abbrName": "Issr", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-Id-PrvtId-Othr-Issr", "children": []}]}]}]}, {"fullName": "CountryOfResidence", "abbrName": "CtryOfRes", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-GrnshmtAdmstr-CtryOfRes", "children": []}]}, {"fullName": "ReferenceNumber", "abbrName": "RefNb", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RefNb", "children": []}, {"fullName": "Date", "abbrName": "Dt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-Dt", "children": []}, {"fullName": "RemittedAmount", "abbrName": "RmtdAmt", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt", "children": [{"fullName": "<PERSON><PERSON><PERSON><PERSON>", "abbrName": "Ccy", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-RmtdAmt-Ccy", "children": []}]}, {"fullName": "FamilyMedicalInsuranceIndicator", "abbrName": "FmlyMdclInsrncInd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-FmlyMdclInsrncInd", "children": []}, {"fullName": "EmployeeTerminationIndicator", "abbrName": "MplyeeTermntnInd", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-GrnshmtRmt-MplyeeTermntnInd", "children": []}]}, {"fullName": "AdditionalRemittanceInformation", "abbrName": "AddtlRmtInf", "nestedAbbrName": "FIToFICstmrCdtTrf-CdtTrfTxInf-RmtInf-Strd-AddtlRmtInf", "children": []}]}]}]}]}