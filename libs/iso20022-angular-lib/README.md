# Lib

This project was generated using [Angular CLI](https://github.com/angular/angular-cli) version 19.2.9.

# Projects

- projects/lib: iso20022-lib
  - FormRule-Dirtective
  - Audohide-Directive
  - ErrorComponent
- projects/test-app
  - Zum testen/entwickeln der lib

# local-dev

- `npm run start` im project-root
- alle component/directves/etc im lib project brauchen eine eigene ng-package.json, public-api.ts und index.ts
- all lib componenten müssen über @helaba/foo importiert werden (auch innerhalb der lib), siehe tsconfig.json

# lib bauen

- `npm run build` im project-root

# im client project nutzen

- `npm install <path/to/lib>/dist/helaba-iso20022-lib-0.0.1.tgz`
