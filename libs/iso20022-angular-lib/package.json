{"name": "lib", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build iso20022-lib && cd dist && npm pack @helaba/iso20022-lib/", "watch": "ng build --watch --configuration development", "test": "ng test", "generate-rules": "ts-node -P rule-generation/tsconfig.scripts.json rule-generation/scripts/main.ts rule-generation/input/pacs.008.001.08_cbprplus/schema.json rule-generation/input/pacs.008.001.08_cbprplus/schema.xsd --output-folder projects/iso20022-lib/rules/generated --base-element fi_to_fi_customer_credit_transfer_v08"}, "private": true, "dependencies": {"@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.0", "typescript": "~5.7.2", "@types/minimist": "^1.2.5", "@types/node": "^22.15.29", "@xmldom/xmldom": "^0.9.8", "minimist": "^1.2.8", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0"}}