import {
  AfterContentInit,
  ContentChildren,
  Directive,
  effect,
  input,
  Input,
  On<PERSON><PERSON>roy,
  QueryList,
} from '@angular/core';
import {
  AbstractControl,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
} from '@angular/forms';
import { AutohideDirective } from './autohide.directive';
import { Rule, affectedFields } from '@helaba/iso20022-lib/rules';
import { Subscription } from 'rxjs';
import {
  conditionValidator,
  getBasicRuleErrors,
  getChangedFields,
} from './utils';

@Directive({
  selector: '[formRules]',
})
export class FormRulesDirective {
  targetFormGroup = input.required<FormGroup>({ alias: 'formGroup' });
  validationRules = input.required<Rule[]>({ alias: 'formRules' });

  // @ContentChildren(AutohideDirective, { descendants: true })
  // autohideChildren?: QueryList<AutohideDirective>;

  //private inputMap: { [key: string]: AutohideDirective } = {};

  #activeSubscriptions: Subscription[] = [];
  #previousFormValues: any = {}; // Store previous form values to detect changes
  #initializationComplete = false;

  constructor() {
    effect((onCleanup) => {
      // Clean up any existing subscriptions when the formGroup changes or component is destroyed.
      this.cleanup();

      this.#initializationComplete = false;

      /**
       * Apply custom ValidatorFn to all form inputs
       */
      for (const [fieldName, control] of Object.entries(
        this.targetFormGroup().controls
      )) {
        const rulesForField = this.validationRules().filter((r) =>
          r.type === 'condition'
            ? r.rules.some((subRule) => subRule.target === fieldName)
            : r.target === fieldName
        );
        if (
          fieldName ===
          'FIToFICstmrCdtTrf-GrpHdr-SttlmInf-InstgRmbrsmntAgt-FinInstnId-BICFI'
        ) {
          console.log('Found rules for field:', fieldName);
          console.log('\trules:', rulesForField);
        }
        if (rulesForField.length > 0) {
          control.addValidators(
            this.getCustomValidatorFn(rulesForField, fieldName)
          );
        }
      }

      // Mark initialization complete AFTER all validators are added
      // Use setTimeout to ensure this runs after all synchronous initialization
      setTimeout(() => {
        this.#initializationComplete = true;
      }, 0);

      // Initiate the 'previousFormValues' with the current values of the form inputs
      this.#previousFormValues = this.targetFormGroup().value;

      /**
       * We have to subscribe to all inputs, since on-change is only triggered if
       * the input happened on that specific field, but we need to check the target form-inputs
       */
      this.#activeSubscriptions.push(
        this.targetFormGroup().valueChanges.subscribe((currentValues) => {
          const changedFields: string[] = getChangedFields(
            this.#previousFormValues,
            currentValues
          );

          console.log('changedFields:', changedFields);

          this.#previousFormValues = currentValues; // Update previous values for next change detection

          // Figure out which other fields are connected to the changed fields via conditional rules
          const affectedFieldsForValueChanges: Set<string> = new Set(
            changedFields.flatMap(
              (changedField) => affectedFields[changedField] || []
            )
          );

          for (const [field, control] of Object.entries(
            this.targetFormGroup().controls
          )) {
            if (!affectedFieldsForValueChanges.has(field)) {
              continue; // Skip fields that are not affected by the changes
            }
            console.log('Updating value and validity for field:', field);
            control.updateValueAndValidity({ onlySelf: true }); // Do not bubble up the change event to the parent as this would again call 'onFormValueChanges' and cause an infinite loop.
          }
        })
      );

      /**
       * Map all autohide elements to their corresponding form input
       */
      // console.debug('AutohideDirectives', this.autohideChildren);
      // this.autohideChildren?.forEach((child) => {
      //   if (child.controlName) {
      //     this.inputMap[child.controlName] = child;
      //   }
      // });
      // console.debug('Found Autohide:', this.inputMap);

      /**
       * Set default visibility for autohide form-inputs
       */
      // for (const field in this.formGroup.controls) {
      //   const targetRules = this.rules.filter((r) => r.target === field);
      //   console.debug('Init Autohide:', field);
      //   if (targetRules.length > 0) {
      //     for (const rule of targetRules) {
      //       if (!this.inputMap[rule.target]) {
      //         continue;
      //       }
      //       if (rule.type === 'displayed') {
      //         this.inputMap[rule.target]?.setVisible(rule.value);
      //       } else if (rule.type === 'condition') {
      //         const visibilityRules = this.getVisibilityRule(rule.rules);
      //         if (visibilityRules === null || visibilityRules.length <= 0) {
      //           continue;
      //         }
      //         const defaultVisibility = this.getDefaultVisibility(targetRules);
      //         this.updateVisibility(
      //           this.inputMap[rule.target],
      //           rule.conditions,
      //           visibilityRules,
      //           defaultVisibility
      //         );
      //       }
      //     }
      //   }
      // }

      /**
       * Update the autohide form-inputs for every input
       * Same reason as above
       */
      // this.subscribtions.push(
      //   this.formGroup.valueChanges.subscribe((values) => {
      //     Object.keys(this.formGroup!.controls).forEach((field) => {
      //       if (this.inputMap[field]) {
      //         const targetRules = this.rules.filter((r) => r.target === field);
      //         for (const rule of targetRules) {
      //           if (rule.type !== 'condition') {
      //             continue;
      //           }
      //           const visibilityRules = this.getVisibilityRule(rule.rules);
      //           if (visibilityRules === null || visibilityRules.length <= 0) {
      //             continue;
      //           }
      //           const defaultVisibility = this.getDefaultVisibility(targetRules);
      //           this.updateVisibility(
      //             this.inputMap[rule.target],
      //             rule.conditions,
      //             visibilityRules,
      //             defaultVisibility
      //           );
      //         }
      //       }
      //     });
      //   })
      // );

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  /**
   * Custom ValidatorFn to combine Validators.Foo with cross-reference rules
   * @param rules Relevant rules for the control
   * @returns Custom ValidatorFn that returns cumulative ValidationErrors in a custom format { [ruleId]: unknown } or null if everything is fine
   */
  private getCustomValidatorFn(
    rules: Array<Rule>,
    fieldName: string
  ): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!this.#initializationComplete) {
        // If initialization is not complete, skip validation as a performance optimization
        return null;
      }

      if (!control.parent) {
        return null;
      }

      const errors: ValidationErrors = {};

      for (const rule of rules) {
        if (rule.type !== 'condition') {
          const basicRuleErrors = getBasicRuleErrors(control, rule);
          if (!basicRuleErrors) {
            continue;
          }
          // We are actually only interested in the rule id, as that is simply turned into an error message which contains the reason. No need to know whether it is a 'required' or 'pattern' error etc.
          // We still save the error object, in case we need it later on.
          errors[rule.id] = basicRuleErrors;
        } else {
          const conditionalRuleErrors = conditionValidator(
            fieldName,
            control,
            rule
          );
          if (!conditionalRuleErrors) {
            continue;
          }
          for (const [nestedRuleId, errorValue] of Object.entries(
            conditionalRuleErrors
          )) {
            // We are actually only interested in the rule id, as that is simply turned into an error message which contains the reason. No need to know whether it is a 'required' or 'pattern' error etc.
            // We still save the error object, in case we need it later on.
            errors[nestedRuleId] = errorValue;
          }
        }
      }

      console.debug('\tErrors:', errors);
      return Object.keys(errors).length > 0 ? errors : null;
    };
  }

  // private getDefaultVisibility(rules: Array<Rule>): boolean {
  //   const visRules = rules.filter((r) => r.type === 'displayed');
  //   if (visRules.length > 0) {
  //     return visRules[visRules.length - 1].value;
  //   } else {
  //     return true;
  //   }
  // }

  // private getVisibilityRule(rules: Array<Rule>): Rule[] | null {
  //   return rules.filter((r) => r.type === 'displayed');
  // }

  // private updateVisibility(
  //   target: AutohideDirective,
  //   conditions: Array<Condition>,
  //   visibilityRules: Array<Rule>,
  //   defaultVisibility = true
  // ) {
  //   console.debug('UpdateVisibility:', target.controlName);
  //   console.debug('\tTarget:', target);
  //   console.debug('\tConditions:', conditions);
  //   console.debug('\tVisabilityRules:', visibilityRules);
  //   console.debug('\tDefaultVisability:', defaultVisibility);
  //   let allConditionsMet = false;
  //   for (const condition of conditions) {
  //     const relatedControl = this.formGroup!.get(condition.field);
  //     if (!relatedControl) {
  //       continue;
  //     }
  //     const relatedValue = relatedControl.value;
  //     let conditionMet = false;

  //     switch (condition.type) {
  //       case 'value':
  //         conditionMet = relatedValue === condition.value;
  //         break;
  //       case 'present':
  //         conditionMet = relatedValue !== null;
  //         break;
  //       case 'notEqual':
  //         const otherField = this.formGroup!.get(condition.otherField);
  //         conditionMet = relatedValue !== otherField?.value;
  //     }

  //     allConditionsMet = conditionMet;
  //     if (!conditionMet) {
  //       // we don't have to check every condition, since all must be met
  //       break;
  //     }
  //   }
  //   console.debug('\tAll conditions met:', allConditionsMet);
  //   if (allConditionsMet) {
  //     for (const rule of visibilityRules) {
  //       if (rule.type === 'displayed') {
  //         target.setVisible(rule.value);
  //       } else {
  //         console.warn('Got a wrong rule in updateVisability: ', rule);
  //       }
  //     }
  //   } else {
  //     target.setVisible(defaultVisibility);
  //   }
  // }

  private cleanup() {
    this.#activeSubscriptions.forEach((sub) => sub.unsubscribe());
    this.#activeSubscriptions = [];
    this.#previousFormValues = {};
  }
}
