import { Directive, ElementRef, Input, OnInit, Renderer2 } from '@angular/core';

@Directive({
  selector: '[autohide]',
})
export class AutohideDirective implements OnInit {
  @Input('autohide') controlName?: string;

  ngOnInit(): void {
    console.debug('Audohide for:', this.controlName);
  }

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  show() {
    this.renderer.setStyle(this.el.nativeElement, 'display', '');
  }

  hide() {
    this.renderer.setStyle(this.el.nativeElement, 'display', 'none');
  }

  setVisible(isVisible: boolean) {
    if (isVisible) {
      this.show();
    } else {
      this.hide();
    }
  }
}
