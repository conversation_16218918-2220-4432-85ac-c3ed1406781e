const basicValidationTypes = [
  'required',
  'prohibited',
  'maxLength',
  'pattern',
  'value',
  'maxItems',
  'contains',
] as const;
export type BasicValidationType = (typeof basicValidationTypes)[number];

export const basicRuleTypes = [
  ...basicValidationTypes,
  'displayed',
  'serverOnly',
] as const;
export type BasicRuleType = (typeof basicRuleTypes)[number];

type BaseRule = {
  id: string;
  description: string;
  type: BasicRuleType | 'condition';
};

type BaseBasicRule = BaseRule & {
  target: string;
};

export type RequiredRule = BaseBasicRule & {
  type: 'required';
  value: boolean;
};

export type ProhibitedRule = BaseBasicRule & {
  type: 'prohibited';
  value: boolean;
};

type MaxLengthRule = BaseBasicRule & {
  type: 'maxLength';
  value: number;
};

export type PatternRule = BaseBasicRule & {
  type: 'pattern';
  value: string;
};

type ValueRule = BaseBasicRule & {
  type: 'value';
  value: string;
  isEqual: boolean;
};

type MaxItemsRule = BaseBasicRule & {
  type: 'maxItems';
  value: number;
};

// E.g. "Data present in structured elements within the Postal Address must not be repeated in AddressLine" -> target: AdrLine, contains: false, otherFields: ['Dept', 'SubDept', 'StrtNm', ...]
type ContainsRule = BaseBasicRule & {
  type: 'contains';
  otherFields: string[];
  contains: boolean;
};

type DisplayedRule = BaseBasicRule & {
  type: 'displayed';
  value: boolean;
};

export type ServerOnlyRule = BaseBasicRule & {
  type: 'serverOnly';
  value: boolean;
};

export type BasicRule =
  | RequiredRule
  | ProhibitedRule
  | MaxLengthRule
  | PatternRule
  | ValueRule
  | MaxItemsRule
  | ContainsRule
  | DisplayedRule;

type BaseCondition = {
  field: string;
};

export type Condition = BaseCondition &
  (
    | {
        type: 'value';
        value: string;
      }
    | {
        type: 'present';
        value: boolean;
      }
    | {
        type: 'notEqual';
        otherField: string;
      }
  );

export type NestedCondition =
  | Condition
  | {
      conditions: Condition[]; // Only nested once.
      conditionsConnector?: 'and' | 'or';
    };

export function isCondition(cond: NestedCondition): cond is Condition {
  return 'type' in cond && 'field' in cond;
}

// The targets of conditional rules are in the rules array
export type ConditionalRule = BaseRule & {
  type: 'condition';
  conditions: NestedCondition[];
  conditionsConnector?: 'and' | 'or';
  rules: BasicRule[];
  rulesConnector?: 'and' | 'or';
};

export type Rule = BasicRule | ConditionalRule | ServerOnlyRule;
