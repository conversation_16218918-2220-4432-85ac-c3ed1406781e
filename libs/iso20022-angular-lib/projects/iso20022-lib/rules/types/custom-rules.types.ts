import { Rule } from './validation-rules.types';

export type CustomRule = Rule & {
  baseKeys?: string[]; // This allows for defining a rule that applies to multiple keys or to just extract the lengthy key into a single place.
};

export type RequiredDefinition =
  | {
      or?: (string | string[])[]; // E.g. ["field1", ["field2", "field3"]] means that either field1 must be present or both field2 and field3 must be present.
    }
  | { and?: string[] };
