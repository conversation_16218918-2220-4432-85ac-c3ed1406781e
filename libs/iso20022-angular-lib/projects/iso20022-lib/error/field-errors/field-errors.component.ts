import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  input,
  signal,
  Signal,
  WritableSignal,
} from '@angular/core';
import { AbstractControl, ValidationErrors } from '@angular/forms';
import { Subscription } from 'rxjs';

@Component({
  selector: 'field-errors',
  templateUrl: './field-errors.component.html',
  styleUrl: './field-errors.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FieldErrorsComponent {
  control = input.required<AbstractControl | null>();

  #currentErrors: WritableSignal<ValidationErrors | null | undefined> =
    signal(undefined);

  // To keep track of the subscription to the control's statusChanges.
  #activeSubscriptions: Subscription[] = [];

  constructor() {
    effect((onCleanup) => {
      // Clean up any existing subscription when the control changes or component is destroyed.
      this.cleanup();

      const currentControl = this.control();

      if (currentControl) {
        this.#activeSubscriptions.push(
          currentControl.statusChanges.subscribe(() => {
            // Whenever the value changes, update the `#currentErrors` signal
            // with the latest errors from the control.
            this.#currentErrors.set(currentControl.errors);
          })
        );
      } else {
        this.#currentErrors.set(null);
      }

      onCleanup(() => {
        this.cleanup();
      });
    });
  }

  erroneousRules: Signal<string[]> = computed(() => {
    const errors = this.#currentErrors();
    if (!errors) {
      return [];
    }
    return Object.keys(errors);
  });

  private cleanup() {
    for (const sub of this.#activeSubscriptions) {
      sub.unsubscribe();
    }
    this.#activeSubscriptions = [];
  }
}
