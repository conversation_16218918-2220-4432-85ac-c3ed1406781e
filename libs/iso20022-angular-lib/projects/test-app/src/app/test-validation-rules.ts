import { isCondition, Rule } from '@helaba/iso20022-lib/rules';

export const testValidationRules: Rule[] = [
  {
    id: 'rule1',
    description: 'Field 1 is required',
    type: 'required',
    target: 'field1',
    value: true,
  },
  {
    id: 'rule2',
    description: 'Field 2 is prohibited',
    type: 'prohibited',
    target: 'field2',
    value: true,
  },
  {
    id: 'rule3',
    description: 'Field 3 has a maximum length of 5 characters',
    type: 'maxLength',
    target: 'field3',
    value: 5,
  },
  {
    id: 'rule4',
    description: 'Field 4 should only contain alphanumeric characters',
    type: 'pattern',
    target: 'field4',
    value: '^[a-zA-Z0-9]*$',
  },
  {
    id: 'rule5',
    description: "Field 5 must have the value 'test'",
    type: 'value',
    target: 'field5',
    value: 'test',
    isEqual: true,
  },
  {
    id: 'rule6',
    description: "Field 6 must not have the value 'test'",
    type: 'value',
    target: 'field6',
    value: 'test',
    isEqual: false,
  },
  {
    id: 'rule7',
    description: 'Field 7 should have a maximum of 3 items',
    type: 'maxItems',
    target: 'field7',
    value: 3,
  },
  {
    id: 'rule8',
    description: 'Field 8 must contain the value from Field 5',
    type: 'contains',
    target: 'field8',
    contains: true,
    otherFields: ['field5'],
  },
  {
    id: 'rule9',
    description: 'Field 8 must not contain the value from Field 6 or Field 7',
    type: 'contains',
    target: 'field8',
    contains: false,
    otherFields: ['field6', 'field7'],
  },
  {
    id: 'rule10',
    description:
      "If Field 1 has the value 'test', then Field 9 is required and Field 10 is prohibited",
    type: 'condition',
    conditions: [
      {
        field: 'field1',
        type: 'value',
        value: 'test',
      },
    ],
    rules: [
      {
        id: 'rule10-field9-required',
        description: "Field 9 is required if Field 1 is 'test'",
        type: 'required',
        target: 'field9',
        value: true,
      },
      {
        id: 'rule10-field10-prohibited',
        description: "Field 10 is prohibited if Field 1 is 'test'",
        type: 'prohibited',
        target: 'field10',
        value: true,
      },
    ],
  },
  {
    id: 'rule11',
    description:
      "If Field 7 is present, then Field 9 has a maximum length of 3 and Field 10 must have a value 'test10'",
    type: 'condition',
    conditions: [
      {
        field: 'field7',
        type: 'present',
        value: true,
      },
    ],
    rules: [
      {
        id: 'rule10-field9-maxLength',
        description: 'Field 9 has a maximum length of 3 if Field 7 is present',
        type: 'maxLength',
        target: 'field9',
        value: 3,
      },
      {
        id: 'rule10-field10-value',
        description:
          "Field 10 must have the value 'test10' if Field 7 is present",
        type: 'value',
        target: 'field10',
        value: 'test10',
        isEqual: true,
      },
    ],
  },
];

const affectedFieldsMap =
  computeAffectedFieldsForFieldValueChange(testValidationRules);

export const affectedFields = mapToObject(affectedFieldsMap);

console.log('Affected Fields:', affectedFields);

function computeAffectedFieldsForFieldValueChange(
  rules: Rule[]
): Map<string, Set<string>> {
  const affectedFieldsForChange: Map<string, Set<string>> = new Map();

  for (const rule of rules) {
    if (rule.type === 'condition') {
      // For conditional rules, if the changed field is part of a condition, all targets of subrules might be affected
      const conditionTargets: string[] = rule.conditions.flatMap((condition) =>
        isCondition(condition)
          ? [condition.field]
          : condition.conditions.map((c) => c.field)
      );

      const subRuleTargets: string[] = rule.rules.map(
        (subRule) => subRule.target
      );

      for (const target of conditionTargets) {
        if (affectedFieldsForChange.has(target)) {
          const affectedFieldsForRule = affectedFieldsForChange.get(target);
          for (const subRuleTarget of subRuleTargets) {
            affectedFieldsForRule?.add(subRuleTarget);
          }
        } else {
          affectedFieldsForChange.set(target, new Set(subRuleTargets));
        }
      }
    } else {
      if (affectedFieldsForChange.has(rule.target)) {
        const affectedFieldsForRule = affectedFieldsForChange.get(rule.target);
        affectedFieldsForRule?.add(rule.target);
      } else {
        affectedFieldsForChange.set(rule.target, new Set([rule.target]));
      }
    }

    if (rule.type === 'contains') {
      // For 'contains' rules, the target of the rule is also affected by changes to the 'otherField's of the rule.
      const otherFields = rule.otherFields;
      for (const otherField of otherFields) {
        if (affectedFieldsForChange.has(otherField)) {
          const affectedFieldsForRule = affectedFieldsForChange.get(otherField);
          affectedFieldsForRule?.add(rule.target);
        } else {
          affectedFieldsForChange.set(otherField, new Set([rule.target]));
        }
      }
    }
  }

  return affectedFieldsForChange;
}

function mapToObject(map: Map<string, Set<string>>): Record<string, string[]> {
  const obj: Record<string, string[]> = {};
  for (const [key, valueSet] of map.entries()) {
    obj[key] = Array.from(valueSet);
  }

  return obj;
}
