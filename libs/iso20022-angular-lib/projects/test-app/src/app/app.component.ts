import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  inject,
} from '@angular/core';
import {
  FormArray,
  FormControl,
  FormGroup,
  NonNullableFormBuilder,
  ReactiveFormsModule,
} from '@angular/forms';
import {
  FormRulesDirective,
  AutohideDirective,
} from '@helaba/iso20022-lib/directives';
import { Rule } from '@helaba/iso20022-lib/rules';
import { FieldErrorsComponent } from '@helaba/iso20022-lib/error';
import { testValidationRules } from './test-validation-rules';
import { TestFormControl, TestFormGroup } from './app.types';

@Component({
  selector: 'app-root',
  imports: [
    ReactiveFormsModule,
    FormRulesDirective,
    AutohideDirective,
    FieldErrorsComponent,
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent {
  fb = inject(NonNullableFormBuilder);

  rules: Rule[] = testValidationRules;

  testFormGroup: TestFormGroup = this.fb.group({
    field1: this.fb.control(''),
    field2: this.fb.control(''),
    field3: this.fb.control(''),
    field4: this.fb.control(''),
    field5: this.fb.control(''),
    field6: this.fb.control(''),
    field7: this.fb.array<TestFormControl>([]),
    field8: this.fb.control(''),
    field9: this.fb.control(''),
    field10: this.fb.control(''),
  });

  addItem() {
    this.testFormGroup.controls.field7.push(this.fb.control(''));
  }

  removeItem(index: number) {
    this.testFormGroup.controls.field7.removeAt(index);
  }

  onSubmit() {
    console.log(this.testFormGroup.getRawValue());
  }
}
