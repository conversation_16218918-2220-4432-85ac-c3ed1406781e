<div>
  <h2>Test Form</h2>

  <form [formGroup]="testFormGroup" [formRules]="rules" (ngSubmit)="onSubmit()">
    <div autohide="field1">
      <label for="field1">Field 1</label>
      <input id="field1" type="text" formControlName="field1" />
      <field-errors
        [control]="testFormGroup.get('field1')"
        fieldName="field1"
      />
    </div>
    <div autohide="field2">
      <label for="field2">Field 2</label>
      <input id="field2" type="text" formControlName="field2" />
      <field-errors
        [control]="testFormGroup.get('field2')"
        fieldName="field2"
      />
    </div>
    <div autohide="field3">
      <label for="field3">Field 3</label>
      <input id="field3" type="text" formControlName="field3" />
      <field-errors
        [control]="testFormGroup.get('field3')"
        fieldName="field3"
      />
    </div>
    <div autohide="field4">
      <label for="field4">Field 4</label>
      <input id="field4" type="text" formControlName="field4" />
      <field-errors
        [control]="testFormGroup.get('field4')"
        fieldName="field4"
      />
    </div>
    <div autohide="field5">
      <label for="field5">Field 5</label>
      <input id="field5" type="text" formControlName="field5" />
      <field-errors
        [control]="testFormGroup.get('field5')"
        fieldName="field5"
      />
    </div>
    <div autohide="field6">
      <label for="field6">Field 6</label>
      <input id="field6" type="text" formControlName="field6" />
      <field-errors
        [control]="testFormGroup.get('field6')"
        fieldName="field6"
      />
    </div>
    <div autohide="field7">
      <label for="field7">Field 7</label>
      <div formArrayName="field7">
        @for (entry of testFormGroup.controls.field7.controls; track index; let
        index = $index;) {
        <div>
          <input type="text" [formControlName]="index" />
          <span (click)="removeItem(index)">X</span>
        </div>
        }
      </div>
      <field-errors
        [control]="testFormGroup.get('field7')"
        fieldName="field7"
      />
      <button (click)="addItem()">Add item</button>
    </div>
    <div autohide="field8">
      <label for="field8">Field 8</label>
      <input id="field8" type="text" formControlName="field8" />
      <field-errors
        [control]="testFormGroup.get('field8')"
        fieldName="field8"
      />
    </div>
    <div autohide="field9">
      <label for="field9">Field 9</label>
      <input id="field9" type="text" formControlName="field9" />
      <field-errors
        [control]="testFormGroup.get('field9')"
        fieldName="field9"
      />
    </div>
    <div autohide="field10">
      <label for="field10">Field 10</label>
      <input id="field10" type="text" formControlName="field10" />
      <field-errors
        [control]="testFormGroup.get('field10')"
        fieldName="field10"
      />
    </div>

    <div>
      <button type="submit" [disabled]="testFormGroup.invalid">Submit</button>
    </div>
  </form>
</div>
